<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Letter Size Page with SVG Background</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900&display=swap">
    
    <style>

        
        @font-face {
            font-family: 'Clash Display';
            src: local('Clash Display'), url('https://fonts.cdnfonts.com/s/65008/ClashDisplayRegular.woff') format('woff');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }
        
        @font-face {
            font-family: 'Clash Display';
            src: local('Clash Display'), url('https://fonts.cdnfonts.com/s/65008/ClashDisplaySemibold.woff') format('woff');
            font-weight: 600;
            font-style: normal;
            font-display: swap;
        }
        
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            background-color: #f0f0f0;
        }
        
        .p1-letter-container {
            width: 21.59cm; /* 612px converted to cm */
            height: 27.94cm; /* 792px converted to cm */
            margin: 0.71cm auto; /* 20px converted to cm */
            position: relative;
            background-color: white;
            box-shadow: 0 0 0.35cm rgba(0, 0, 0, 0.1); /* 10px converted to cm */
            overflow: hidden;
        }
        
        .p1-background-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .p1-content {
            position: relative;
            z-index: 1;
            padding: 2cm;
            height: 100%;
            box-sizing: border-box;
        }

        .p1-report-container {
            position: absolute;
            width: 100%;
            height: 6.38cm; /* 181px converted to cm */
            left: 2.26cm; /* 64px converted to cm */
            bottom: calc(3.62cm + 2.12cm); /* Positioned right above the logo */
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            z-index: 2;
        }
        
        .p1-report-title {
            color: black;
            font-size: 1.09cm; /* 31px converted to cm */
            font-weight: 600;
            font-family: 'Clash Display', sans-serif;
            margin-bottom: 0;
            line-height: 1.2;
        }
        
        .p1-subtitle-spacing {
            color: black;
            font-size: 0.85cm; /* 24px converted to cm */
            font-weight: 600;
            font-family: 'Clash Display', sans-serif;
            margin: 1rem 0;
            line-height: 1;
        }
        
        .p1-report-details {
            font-family: 'Clash Display', sans-serif;
            color: black;
            line-height: 1.5;
        }
        
        .p1-fund-name-label {
            font-size: 0.42cm; /* 12px converted to cm */
            font-weight: 600;
        }
        
        .p1-fund-name-value {
            font-size: 0.42cm; /* 12px converted to cm */
            font-weight: 400;
        }
        
        .p1-reporting-period-label {
            font-size: 0.42cm; /* 12px converted to cm */
            font-weight: 600;
        }
        
        .p1-reporting-period-value {
            font-size: 0.42cm; /* 12px converted to cm */
            font-weight: 400;
        }

        .p1-logo-box {
            position: absolute;
            width: 2.82cm; /* 80px converted to cm */
            height: 2.12cm; /* 60px converted to cm */
            bottom: 3.62cm; /* 102.63px converted to cm */
            left: 2.61cm; /* 74px converted to cm */
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2;
        }

        .p1-logo-svg {
            width: 100%;
            height: 100%;
            object-fit: contain;
            max-width: 100%;
            max-height: 100%;
        }
        
        .p1-company-name {
            position: absolute;
            left: calc(2.61cm + 2.82cm + 0.56cm); /* Logo left position + logo width + 16px in cm */
            /* Calculate vertical center position of the logo:
               Logo bottom position + half of logo height - half of current font size */
            bottom: calc(3.62cm + (2.12cm / 2) - (0.52cm / 2)); /* Centering with font size of 0.52cm (≈ 14.7px) */
            display: flex;
            align-items: center;
            z-index: 2;
            font-family: 'Gotham Black', "Montserrat", sans-serif;
            font-size: 0.52cm;
            line-height: 1;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.12cm; 
            word-spacing: 0cm;
            color: #000;
        }

        @media print {
            body {
                background-color: transparent;
            }
            
            .p1-letter-container {
                margin: 0;
                box-shadow: none;
                width: 21.59cm; /* 612px converted to cm */
                height: 27.94cm; /* 792px converted to cm */
            }
        }
    </style>
</head>
<body>
    <div class="p1-letter-container">
        <svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg" class="p1-background-svg">
            <g clip-path="url(#clip0_2008_2)">
            <rect width="612" height="792" fill="white"/>
            <g opacity="0.4" filter="url(#filter0_f_2008_2)">
            <path d="M-45.1781 411.113C-49.6944 415.201 -56.0779 416.523 -61.8466 414.611L-158.41 382.604C-158.499 382.574 -158.41 382.604 -158.499 382.574L9.49567 231.05L-64.0465 206.674L-45.0115 149.247C-42.0676 140.365 -32.4795 135.554 -23.5936 138.499L181.661 206.534" fill="url(#paint0_linear_2008_2)"/>
            </g>
            <rect opacity="0.4" x="614.354" y="595.621" width="72.0003" height="362.138" transform="rotate(-150 614.354 595.621)" fill="url(#paint1_linear_2008_2)"/>
            <g opacity="0.5" filter="url(#filter1_f_2008_2)">
            <ellipse cx="623" cy="486.499" rx="94" ry="93.5" fill="url(#paint2_linear_2008_2)"/>
            </g>
            <g opacity="0.5" filter="url(#filter2_f_2008_2)">
            <ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint3_linear_2008_2)"/>
            </g>
            </g>
            <defs>
            <filter id="filter0_f_2008_2" x="-222.499" y="73.6331" width="468.161" height="405.838" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feGaussianBlur stdDeviation="32" result="effect1_foregroundBlur_2008_2"/>
            </filter>
            <filter id="filter1_f_2008_2" x="329" y="192.999" width="588" height="587" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2008_2"/>
            </filter>
            <filter id="filter2_f_2008_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2008_2"/>
            </filter>
            <linearGradient id="paint0_linear_2008_2" x1="273.858" y1="154.945" x2="-484.695" y2="392.721" gradientUnits="userSpaceOnUse">
            <stop stop-color="#D1F15E"/>
            <stop offset="0.94" stop-color="#70E9EF"/>
            </linearGradient>
            <linearGradient id="paint1_linear_2008_2" x1="587.2" y1="991.104" x2="845.275" y2="953.261" gradientUnits="userSpaceOnUse">
            <stop stop-color="#D1F15E"/>
            <stop offset="0.94" stop-color="#70E9EF"/>
            </linearGradient>
            <linearGradient id="paint2_linear_2008_2" x1="62.732" y1="735.245" x2="747.74" y2="-77.0686" gradientUnits="userSpaceOnUse">
            <stop stop-color="#D1F15E"/>
            <stop offset="0.94" stop-color="#70E9EF"/>
            </linearGradient>
            <linearGradient id="paint3_linear_2008_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509" gradientUnits="userSpaceOnUse">
            <stop stop-color="#D1F15E"/>
            <stop offset="0.94" stop-color="#70E9EF"/>
            </linearGradient>
            <clipPath id="clip0_2008_2">
            <rect width="612" height="792" fill="white"/>
            </clipPath>
            </defs>
        </svg>
        <div class="p1-content">
            
        </div>
        
        <div class="p1-report-container">
            <div class="p1-report-title">{{ $var.report_title }}</div>
            <div class="p1-subtitle-spacing">&nbsp;</div>
            <div class="p1-report-details">
                <span class="p1-fund-name-label">Fund Name: </span>
                <span class="p1-fund-name-value">{{ $var.fund_name }}</span>
                <br>
                <span class="p1-reporting-period-label">Reporting Period: </span>
                <span class="p1-reporting-period-value">{{ $var.reporting_period }}</span>
            </div>
        </div>
        
        <div class="p1-logo-box">
            <img class="p1-logo-svg" src="{{ $var.logo_url }}" alt="Company Logo">
        </div>
        <div class="p1-company-name">{{ $var.company_name }}</div>
        
    </div>
</body>
</html>
