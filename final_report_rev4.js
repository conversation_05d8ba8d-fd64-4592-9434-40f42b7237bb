// Import jsdom to provide DOMParser functionality in Node.js
const { JSDOM } = await import("npm:jsdom");
const { DOMParser } = new JSDOM().window;

const page_html = `<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, target-densitydpi=96, user-scalable=no">
    <title>Rendyr Report Pages</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900&display=swap">
    <link rel="stylesheet" href="https://api.fontshare.com/v2/css?f[]=clash-display@400,500,600&display=swap">
    <style>
        /* Base styles for the document */
        :root {
            --device-dpi: 96dpi;
            --px-per-cm: 37.7952755906; /* 96 / 2.54 (cm per inch) */
        }
        
        body {
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            background-color: #f0f0f0;
            padding-top: 20px;
            padding-bottom: 20px;
        }
        
        /* Page container */
        .p6-page {
            width: 21.59cm;
            height: 27.94cm;
            position: relative;
            background-color: white;
            box-shadow: 0 0 0.35cm rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
           
            page-break-after: always;
        }
        
        /* Common template elements */
        .p6-background-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .p6-header {
            position: absolute;
            top: 3.39cm;
            left: 2.19cm;
            color: black;
            font-size: 0.53cm;
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            z-index: 2;
        }
        
        .p6-logo-box {
            position: absolute;
            width: 1.62cm;
            height: 1.16cm;
            bottom: 1.06cm;
            right: 1.13cm;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2;
        }
        
        /* Container for update items */
        .p6-update-container, .update-container {
            position: absolute;
            top: 6.05cm;
            left: 2.19cm;
            right: 1.13cm;
            bottom: 3.2cm;
            z-index: 2;
            display: flex;
            flex-direction: column;
            gap: 0.1cm;
           
        }
        
        /* Styles for update items */
        .p6-update-item, .update-item {
            margin-bottom: 1cm;
        }
        
        .p6-company-logo-box {
            width: 4.3cm;
            height: 1.09cm;
            display: flex;
            justify-content: flex-start;
            align-items: center;
           
            object-fit: contain;
        }
        
        .p6-company-logo-box img {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
            object-fit: contain;
        }
        
        .p6-fund-boxes-container {
            margin-top: 0.28cm;
            display: flex;
            gap: 0.53cm;
            justify-content: space-between;
            width: 100%;
        }
        
        .p6-fund-box {
            min-width: 1.5cm;
            height: 0.6cm;
            background: #B2D1B2;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Clash Display', sans-serif;
            font-size: 0.32cm;
            padding-left: 0.2cm;
            padding-right: 0.2cm;
            font-weight: 500;
            white-space: nowrap;
        }
        
        .p6-fund-box .p6-rank-value {
            font-weight: 700;
            margin-left: 0.14cm;
        }
        
        .p6-fund-boxes-left {
            display: flex;
            gap: 0.53cm;
        }
        
        .p6-website-text {
            color: #5CD468;
            font-size: 0.42cm;
            font-family: 'Clash Display', sans-serif;
            font-weight: 400;
            line-height: 0.64cm;
            word-wrap: break-word;
            align-self: center;
        }
        
        .p6-report-title {
            color: black;
            font-size: 0.42cm;
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            line-height: 0.64cm;
            word-wrap: break-word;
            margin-top: 0.28cm;
        }
        
        .p6-report-body {
            color: black;
            font-size: 0.42cm;
            font-family: 'Clash Display', sans-serif;
            font-weight: 400;
            line-height: 0.64cm;
            word-wrap: break-word;
            margin-top: 0.28cm;
        }
        
        /* Continuation item styling */
        .p6-continuation-header {
            border-left: 0.1cm solid #5CD468;
            padding-left: 0.4cm;
            margin-bottom: 0.4cm;
        }
        
        .p6-update-item.p6-continuation {
            position: relative;
        }
        
        .p6-update-item.p6-continuation .p6-report-title {
            font-style: italic;
        }
        
        .p6-update-item.p6-continuation .p6-report-body {
            position: relative;
        }

        /* Letter container for printing */
        .p6-letter-container {
            width: 21.59cm;
            height: 27.94cm;
        }

        /* Font declarations */
        @font-face {
            font-family: 'Montserrat';
            src: local('Montserrat'), url('https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900&display=swap') format('woff');
            font-weight: 700;
            font-display: swap;
        }

        @font-face {
            font-family: 'Clash Display';
            src: local('Clash Display'), url('https://fonts.cdnfonts.com/s/65008/ClashDisplayRegular.woff') format('woff');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'Clash Display';
            src: local('Clash Display'), url('https://fonts.cdnfonts.com/s/65008/ClashDisplaySemibold.woff') format('woff');
            font-weight: 600;
            font-style: normal;
            font-display: swap;
        }
        
        /* Print styles */
        @media print {
            body {
                background-color: transparent;
                margin: 0;
                padding: 0;
                gap: 0;
                height: 27.94cm;
                width: 21.59cm;
                // overflow: hidden;
                display: block;
            }
            
            .p6-page, .p6-letter-container {
                margin: 0;
                box-shadow: none;
                width: 21.59cm;
                height: 27.94cm;
            }
            
            .p6-page-container {
                gap: 0;
            }
            
            @page {
                margin: 0;
                padding: 0;
                size: letter;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                color-adjust: exact;
            }
            
            /* Force background images and colors to print */
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            .p6-background-svg {
                display: block !important;
                visibility: visible !important;
            }
            
            /* Ensure continuation styling prints properly */
            .p6-update-item.p6-continuation::before,
            .p6-continuation-header {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            #page1-container, #page2-container, #page3-container {
                page-break-after: always;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact;
                position: relative !important;
                top: 0 !important;
                left: 0 !important;
                margin: 0 !important;
                padding: 0 !important;
                height: 27.94cm !important;
                width: 21.59cm !important;
                overflow: hidden !important;
                display: block !important;
            }
            
            /* Fix positioning for print */
            #page1-report-container, #page1-logo-box, #company-name,
            #page2-logo-box, #page2-content, #page2-title,
            #page3-logo-box, #page3-title, #metrics-container, #additional-metrics, #cashflow-container {
                position: absolute !important;
                transform: none !important;
                float: none !important;
            }
            
            #company-logo, svg {
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
            }
        }
    </style>
</head>

<body>
    <!-- PAGE 1 -->
    <div
        style="width: 21.59cm; height: 27.94cm; position: relative; background-color: white; box-shadow: 0 0 0.35cm rgba(0, 0, 0, 0.1); overflow: hidden;" id="page1-container">
        <!-- Background SVG -->
        <svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg"
            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover;" id="page1-background">
            <g clip-path="url(#clip0_2008_2)">
                <rect width="612" height="792" fill="white" />
                <g opacity="0.4" filter="url(#filter0_f_2008_2)">
                    <path
                        d="M-45.1781 411.113C-49.6944 415.201 -56.0779 416.523 -61.8466 414.611L-158.41 382.604C-158.499 382.574 -158.41 382.604 -158.499 382.574L9.49567 231.05L-64.0465 206.674L-45.0115 149.247C-42.0676 140.365 -32.4795 135.554 -23.5936 138.499L181.661 206.534"
                        fill="url(#paint0_linear_2008_2)" />
                </g>
                <rect opacity="0.4" x="614.354" y="595.621" width="72.0003" height="362.138"
                    transform="rotate(-150 614.354 595.621)" fill="url(#paint1_linear_2008_2)" />
                <g opacity="0.5" filter="url(#filter1_f_2008_2)">
                    <ellipse cx="623" cy="486.499" rx="94" ry="93.5" fill="url(#paint2_linear_2008_2)" />
                </g>
                <g opacity="0.5" filter="url(#filter2_f_2008_2)">
                    <ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint3_linear_2008_2)" />
                </g>
            </g>
            <defs>
                <filter id="filter0_f_2008_2" x="-222.499" y="73.6331" width="468.161" height="405.838"
                    filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                    <feGaussianBlur stdDeviation="32" result="effect1_foregroundBlur_2008_2" />
                </filter>
                <filter id="filter1_f_2008_2" x="329" y="192.999" width="588" height="587" filterUnits="userSpaceOnUse"
                    color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                    <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2008_2" />
                </filter>
                <filter id="filter2_f_2008_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse"
                    color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                    <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2008_2" />
                </filter>
                <linearGradient id="paint0_linear_2008_2" x1="273.858" y1="154.945" x2="-484.695" y2="392.721"
                    gradientUnits="userSpaceOnUse">
                    <stop stop-color="#D1F15E" />
                    <stop offset="0.94" stop-color="#70E9EF" />
                </linearGradient>
                <linearGradient id="paint1_linear_2008_2" x1="587.2" y1="991.104" x2="845.275" y2="953.261"
                    gradientUnits="userSpaceOnUse">
                    <stop stop-color="#D1F15E" />
                    <stop offset="0.94" stop-color="#70E9EF" />
                </linearGradient>
                <linearGradient id="paint2_linear_2008_2" x1="62.732" y1="735.245" x2="747.74" y2="-77.0686"
                    gradientUnits="userSpaceOnUse">
                    <stop stop-color="#D1F15E" />
                    <stop offset="0.94" stop-color="#70E9EF" />
                </linearGradient>
                <linearGradient id="paint3_linear_2008_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509"
                    gradientUnits="userSpaceOnUse">
                    <stop stop-color="#D1F15E" />
                    <stop offset="0.94" stop-color="#70E9EF" />
                </linearGradient>
                <clipPath id="clip0_2008_2">
                    <rect width="612" height="792" fill="white" />
                </clipPath>
            </defs>
        </svg>

        <!-- Report Container -->
        <div
            style="position: absolute; width: 100%; height: 6.38cm; left: 2.26cm; bottom: calc(3.62cm + 2.12cm); display: flex; flex-direction: column; justify-content: flex-start; z-index: 2;" id="page1-report-container">
            <div
                style="color: black; font-size: 1.09cm; font-weight: 600; font-family: 'Clash Display', sans-serif; margin-bottom: 0; line-height: 1.2;" id="report-title">
                Q3 2023 Investor Report</div>
            <div
                style="color: black; font-size: 0.85cm; font-weight: 600; font-family: 'Clash Display', sans-serif; margin: 1rem 0; line-height: 1;">
                &nbsp;</div>
            <div style="font-family: 'Clash Display', sans-serif; color: black; line-height: 1.5;" id="report-details">
                <span style="font-size: 0.42cm; font-weight: 600;">Fund Name: </span>
                <span style="font-size: 0.42cm; font-weight: 400;" id="fund-name">Growth Equity Fund II</span>
                <br>
                <span style="font-size: 0.42cm; font-weight: 600;">Reporting Period: </span>
                <span style="font-size: 0.42cm; font-weight: 400;" id="reporting-period">Q3 2023</span>
            </div>
        </div>

        <!-- Logo Box -->
        <div
            style="position: absolute; width: 2.82cm; height: 2.12cm; bottom: 3.62cm; left: 2.61cm; display: flex; justify-content: center; align-items: center; z-index: 2;" id="page1-logo-box">
            <img style="width: 100%; height: 100%; object-fit: contain; max-width: 100%; max-height: 100%;"
                src="https://storage.googleapis.com/x8xh-wdgz-rmil.n7e.xano.io/vault/gQxBdSlG/46fp2_mWEeQzS8H27JjH58q90tI/2SMsUg../rendyr-logo-colored.svg"
                alt="Company Logo" id="company-logo">
        </div>

        <!-- Company Name -->
        <div
            style="position: absolute; left: calc(2.61cm + 2.82cm + 0.56cm); bottom: calc(3.62cm + (2.12cm / 2) - (0.52cm / 2)); display: flex; align-items: center; z-index: 2; font-family: 'Gotham Black', 'Montserrat', sans-serif; font-size: 0.52cm; line-height: 1; font-weight: 700; text-transform: uppercase; letter-spacing: 0.12cm; word-spacing: 0cm; color: #000;" id="company-name">
            RENDYR</div>
    </div>

    <!-- PAGE 2 -->
    <div
        style="width: 21.59cm; height: 27.94cm; position: relative; background-color: white; box-shadow: 0 0 0.35cm rgba(0, 0, 0, 0.1); overflow: hidden;" id="page2-container">
        <!-- Background SVG -->
        <svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg"
            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover;" id="page2-background">
            <g clip-path="url(#clip0_2020_2)">
                <rect width="612" height="792" fill="white" />
                <g opacity="0.5" filter="url(#filter0_f_2020_2)">
                    <ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint0_linear_2020_2)" />
                </g>
            </g>
            <defs>
                <filter id="filter0_f_2020_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse"
                    color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                    <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2020_2" />
                </filter>
                <linearGradient id="paint0_linear_2020_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509"
                    gradientUnits="userSpaceOnUse">
                    <stop stop-color="#D1F15E" />
                    <stop offset="0.94" stop-color="#70E9EF" />
                </linearGradient>
                <clipPath id="clip0_2020_2">
                    <rect width="612" height="792" fill="white" />
                </clipPath>
            </defs>
        </svg>

        <!-- Logo Box -->
        <div
            style="position: absolute; width: 1.62cm; height: 1.16cm; bottom: 1.06cm; right: 1.13cm; display: flex; justify-content: center; align-items: center; z-index: 2; overflow: hidden;" id="page2-logo-box">
            <svg width="46" height="33" viewBox="0 0 46 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M0.00137067 20.9346V32.9975H8.10509V19.7046H1.22966C0.550474 19.7046 0 20.2552 0 20.9346H0.00137067Z"
                    fill="#222222" />
                <path
                    d="M17.3624 7.27033C18.0416 7.27033 18.592 6.71971 18.592 6.04034V0H3.49102H1.23161C0.552425 0 0.00195312 0.550616 0.00195312 1.22998V13.097H8.10568V7.27033H17.3624Z"
                    fill="#222222" />
                <path
                    d="M27.5053 20.9346V32.9975H35.609V19.7046H28.7336C28.0544 19.7046 27.5039 20.2552 27.5039 20.9346H27.5053Z"
                    fill="#222222" />
                <path
                    d="M19.0574 13.7188C19.2751 13.334 19.6846 13.097 20.1255 13.097H27.5062V1.22998C27.5062 0.550616 28.0567 0 28.7359 0H30.8994H46.0004V6.04034C46.0004 6.71971 45.45 7.27033 44.7708 7.27033H35.6099V13.097H27.513L19.4025 27.3774H25.0236V31.77C25.0236 32.4494 24.4731 33 23.7939 33H8.10547"
                    fill="#222222" />
                <path
                    d="M35.6113 19.7059H42.2512C43.1399 19.7059 43.8602 18.9855 43.8602 18.0965V13.0972H35.6113V19.7059Z"
                    fill="#222222" />
                <path
                    d="M8.08789 19.7059H11.1675C12.0562 19.7059 12.7765 18.9855 12.7765 18.0965V13.0972H8.08789V19.7059Z"
                    fill="#222222" />
            </svg>
        </div>

        <!-- Text Box -->
        <div
            style="position: absolute; top: 3.39cm; left: 2.19cm; bottom: 3.39cm; width: 16.90cm; overflow: hidden; font-family: 'Clash Display', sans-serif; z-index: 1;" id="page2-content">
            <div style="width: 100%; height: 1.45cm; display: flex; align-items: center;">
                <div
                    style="color: black; font-size: 0.85cm; font-family: 'Clash Display', sans-serif; font-weight: 600; word-wrap: break-word; width: 100%;" id="executive-summary-title">
                    Executive Summary</div>
            </div>
            <div id="exec-paragraph">

            </div>
        </div>
    </div>
    
    <div id="pages-container"></div>
    
    <!-- Template for individual pages - will be cloned by JavaScript -->
    <template id="page-template">
        <div class="p6-page">
            <!-- Background SVG -->
            <svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg"
                class="p6-background-svg">
                <g clip-path="url(#clip0_2020_2)">
                    <rect width="612" height="792" fill="white" />
                    <g opacity="0.5" filter="url(#filter0_f_2020_2)">
                        <ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint0_linear_2020_2)" />
                    </g>
                </g>
                <defs>
                    <filter id="filter0_f_2020_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse"
                        color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix" />
                        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                        <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2020_2" />
                    </filter>
                    <linearGradient id="paint0_linear_2020_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#D1F15E" />
                        <stop offset="0.94" stop-color="#70E9EF" />
                    </linearGradient>
                    <clipPath id="clip0_2020_2">
                        <rect width="612" height="792" fill="white" />
                    </clipPath>
                </defs>
            </svg>

            <!-- Fixed header element -->
            <div class="p6-header">Portfolio Company Updates</div>

            <!-- Container for update items - will be filled by JavaScript -->
            <div class="p6-update-container"></div>

            <!-- Fixed logo element -->
            <div class="p6-logo-box">
                <svg width="46" height="33" viewBox="0 0 46 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0.00137067 20.9346V32.9975H8.10509V19.7046H1.22966C0.550474 19.7046 0 20.2552 0 20.9346H0.00137067Z" fill="#222222"/>
                    <path d="M17.3624 7.27033C18.0416 7.27033 18.592 6.71971 18.592 6.04034V0H3.49102H1.23161C0.552425 0 0.00195312 0.550616 0.00195312 1.22998V13.097H8.10568V7.27033H17.3624Z" fill="#222222"/>
                    <path d="M27.5053 20.9346V32.9975H35.609V19.7046H28.7336C28.0544 19.7046 27.5039 20.2552 27.5039 20.9346H27.5053Z" fill="#222222"/>
                    <path d="M19.0574 13.7188C19.2751 13.334 19.6846 13.097 20.1255 13.097H27.5062V1.22998C27.5062 0.550616 28.0567 0 28.7359 0H30.8994H46.0004V6.04034C46.0004 6.71971 45.45 7.27033 44.7708 7.27033H35.6099V13.097H27.513L19.4025 27.3774H25.0236V31.77C25.0236 32.4494 24.4731 33 23.7939 33H8.10547" fill="#222222"/>
                    <path d="M35.6113 19.7059H42.2512C43.1399 19.7059 43.8602 18.9855 43.8602 18.0965V13.0972H35.6113V19.7059Z" fill="#222222"/>
                    <path d="M8.08789 19.7059H11.1675C12.0562 19.7059 12.7765 18.9855 12.7765 18.0965V13.0972H8.08789V19.7059Z" fill="#222222"/>
                </svg>                    
            </div>
        </div>
    </template>
    
    <!-- PAGE 7 PLACEHOLDER -->

    <!-- PAGE 8 PLACEHOLDER -->

   
</body>

</html>`

/**
 * Portfolio Updates Template System
 * 
 * This script handles the pagination and generation of company update pages,
 * ensuring proper distribution of content across multiple pages with identical structure.
 */

// Debug configuration
const DEBUG = {
    enabled: false,  // Always enable logging
    visualizeSplits: false,  // No visual splits
    logMeasurements: false,  // No measurement logs
    showGrids: false  // No measurement grids
};

// Create a DOM parser to work with the page_html content
const parser = new DOMParser();
let htmlDoc = parser.parseFromString(page_html, 'text/html');

/**
 * Enable debug mode with visual indicators - No longer needed but kept for reference
 */
function enableDebugMode() {
    // Function is now empty - kept for compatibility only
    console.log('Standard logging is enabled by default');
}

/**
 * Initializes the portfolio template system with the provided data
 * @param {Object} data - The portfolio company data
 */
function initializePortfolioTemplate(data) {
    const pagesContainer = htmlDoc.getElementById('pages-container');
    const companies = data.companies || [];
    
    // Clear any existing content
    pagesContainer.innerHTML = '';
    
    // Create the first page
    const firstPage = createNewPage();
    pagesContainer.appendChild(firstPage);
    
    let currentPage = firstPage;
    let currentContainer = currentPage.querySelector('.p6-update-container');
    
    // Calculate available height for content
    const maxContainerHeight = getMaxContainerHeight();
    let remainingHeight = maxContainerHeight;
    
    
    // Sort companies alphabetically by name
    companies.sort((a, b) => a.name.localeCompare(b.name));
    
    // Process each company and distribute across pages
    companies.forEach((company, index) => {
        if (DEBUG.enabled) {
            console.log(`Remaining height: ${remainingHeight.toFixed(2)}cm`);
        }
        
        // Create full update item
        const fullUpdateItem = createUpdateItemElement(company);
        const fullItemHeight = calculateElementHeight(fullUpdateItem);
        const fullItemHeightPx = calculateElementHeight(fullUpdateItem, true);
        
        if (DEBUG.enabled) {
            console.log(`Full item height: ${fullItemHeight.toFixed(2)}cm (${Math.round(fullItemHeightPx)}px)`);
        }
        
        // Check if entire item fits on current page
        if (fullItemHeight <= remainingHeight) {
            // Item fits entirely - add it to the page
            currentContainer.appendChild(fullUpdateItem);
            remainingHeight -= fullItemHeight;
            
            // Add debug visualization
            if (DEBUG.enabled) {
                console.log(`Item fits entirely. Remaining height: ${remainingHeight.toFixed(2)}cm`);
            }
        } else {
            // Item doesn't fit entirely - try partial header with logo, fund, and title
            if (DEBUG.enabled) {
                console.log(`Item does not fit entirely. Trying partial header...`);
            }
            
            // Try to fit the partial header (logo, fund boxes, and one line of title)
            const partialHeaderResult = createPartialHeaderElement(company);
            const partialHeaderItem = partialHeaderResult.element;
            const remainingBodyText = partialHeaderResult.remainingText;
            const partialHeaderHeight = calculateElementHeight(partialHeaderItem);
            const partialHeaderHeightPx = calculateElementHeight(partialHeaderItem, true);
            
            if (DEBUG.enabled) {
                console.log(`Partial header height: ${partialHeaderHeight.toFixed(2)}cm (${Math.round(partialHeaderHeightPx)}px)`);
            }
            
            // If the partial header fits, add it and continue to next page with body
            if (partialHeaderHeight <= remainingHeight) {
                if (DEBUG.enabled) {
                    console.log(`Partial header fits. Adding to current page.`);
                }
                
                // Add the partial header to the current page
                currentContainer.appendChild(partialHeaderItem);
                remainingHeight -= partialHeaderHeight;
                
                // Create new page for body content
                currentPage = createNewPage();
                pagesContainer.appendChild(currentPage);
                currentContainer = currentPage.querySelector('.p6-update-container');
                remainingHeight = maxContainerHeight;
                
                // Create continuation item with only the remaining text (not duplicating content)
                const companyWithRemainingText = {...company, report_body: remainingBodyText};
                const continuationItem = createContinuationElement(companyWithRemainingText);
                currentContainer.appendChild(continuationItem);
                
                // Update remaining height
                const continuationHeight = calculateElementHeight(continuationItem);
                const continuationHeightPx = calculateElementHeight(continuationItem, true);
                remainingHeight -= continuationHeight;
                
                // Add debug visualization
                if (DEBUG.enabled) {
                    console.log(`Added continuation item. Remaining height: ${remainingHeight.toFixed(2)}cm`);
                }
            } else {
                // Even partial header doesn't fit - start on a new page
                if (DEBUG.enabled) {
                    console.log(`Partial header doesn't fit. Creating new page.`);
                }
                
                // Move to a new page
                currentPage = createNewPage();
                pagesContainer.appendChild(currentPage);
                currentContainer = currentPage.querySelector('.p6-update-container');
                remainingHeight = maxContainerHeight;
                
                // Try again with the full item on the new page
                if (fullItemHeight <= remainingHeight) {
                    // Full item fits on new page
                    currentContainer.appendChild(fullUpdateItem);
                    remainingHeight -= fullItemHeight;
                    
                    if (DEBUG.enabled) {
                        console.log(`Full item fits on new page. Remaining height: ${remainingHeight.toFixed(2)}cm`);
                    }
                } else {
                    // Full item still doesn't fit - need to split body text
                    if (DEBUG.enabled) {
                        console.log(`Full item still doesn't fit on new page. Splitting content...`);
                    }
                    
                    // Add the header to the current page
                    const headerItem = createCompanyHeaderElement(company);
                    currentContainer.appendChild(headerItem);
                    
                    // Update remaining height after adding header
                    const headerHeight = calculateElementHeight(headerItem);
                    remainingHeight -= headerHeight;
                    
                    // Calculate available space for report body text on current page
                    const availableForBody = remainingHeight;
                    
                    if (availableForBody <= 0) {
                        // Not enough space for any body text after header
                        // Create new page for body content
                        currentPage = createNewPage();
                        pagesContainer.appendChild(currentPage);
                        currentContainer = currentPage.querySelector('.p6-update-container');
                        remainingHeight = maxContainerHeight;
                        
                        // Add continuation with full body text
                        const continuationItem = createContinuationElement(company);
                        currentContainer.appendChild(continuationItem);
                        remainingHeight -= calculateElementHeight(continuationItem);
                    } else {
                        // Split the text to fit in available space
                        const { firstPartItem, secondPartItem, firstPartHeight } = splitReportBodyText(
                            company, 
                            availableForBody
                        );
                        
                        // Replace the header with the first part
                        currentContainer.removeChild(headerItem);
                        currentContainer.appendChild(firstPartItem);
                        remainingHeight = maxContainerHeight - firstPartHeight;
                        
                        // Create new page for second part
                        currentPage = createNewPage();
                        pagesContainer.appendChild(currentPage);
                        currentContainer = currentPage.querySelector('.p6-update-container');
                        remainingHeight = maxContainerHeight;
                        
                        // Add second part to new page
                        currentContainer.appendChild(secondPartItem);
                        remainingHeight -= calculateElementHeight(secondPartItem);
                    }
                }
            }
        }
        
        if (DEBUG.enabled) {
        }
    });
    
    // Log pagination info
    const totalPages = pagesContainer.querySelectorAll('.p6-page').length;
    console.log(`Created ${totalPages} pages for ${companies.length} companies`);
    
    // Convert the document back to string
    return htmlDoc.documentElement.outerHTML;
}

/**
 * Creates a new page from the template
 * @returns {HTMLElement} The new page element
 */
function createNewPage() {
    const pageTemplate = htmlDoc.getElementById('page-template');
    const newPage = pageTemplate.content.cloneNode(true).firstElementChild;
    return newPage;
}

/**
 * Calculates the maximum height available for update items
 * @returns {number} The maximum height in cm
 */
function getMaxContainerHeight() {
    // Fixed container dimensions from CSS
    // top: 6.05cm, bottom: 3.2cm in a letter page (27.94cm)
    // Add a small safety margin (0.2cm) to prevent overflow
    return 27.94 - 6.05 - 3.2 - 0.2;
}

/**
 * Calculates the height an element would have when rendered
 * @param {HTMLElement} element - The element to measure
 * @param {boolean} returnRawPx - If true, returns the raw pixel value instead of converted cm
 * @returns {number} The element's height in cm (or px if returnRawPx is true)
 */
function calculateElementHeight(element, returnRawPx = false) {
    // Create a temporary container for measurement with the correct width constraints
    const tempContainer = htmlDoc.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.visibility = 'hidden';
    
    // Use exact container dimensions from CSS - left: 2.19cm, right: 1.13cm from a 21.59cm page width
    const pageWidth = 21.59; // Letter width in cm
    const leftMargin = 2.19; // Left margin in cm
    const rightMargin = 1.13; // Right margin in cm
    const contentWidth = pageWidth - leftMargin - rightMargin; // Actual content width
    tempContainer.style.width = contentWidth + 'cm';
    
    tempContainer.style.left = '-9999px';
    tempContainer.style.top = '-9999px';
    
    // Clone the element to avoid modifying the original
    const clone = element.cloneNode(true);
    tempContainer.appendChild(clone);
    htmlDoc.body.appendChild(tempContainer);
    
    // For our measurement approximation (since we can't actually render),
    // use a character-based approach to estimate height
    const textContent = clone.textContent || '';
    const charCount = textContent.length;
    
    // Estimated parameters for character-to-height conversion
    // These values are approximate and could be adjusted based on font settings
    const charsPerLine = 70; // Estimated characters per line
    const lineHeightCm = 0.63; // Line height from CSS (in cm)
    const lineHeightPx = lineHeightCm * 37.7952755906; // Convert to pixels
    
    // Calculate estimated lines and height
    const estimatedLines = Math.max(1, Math.ceil(charCount / charsPerLine));
    const estimatedHeightCm = estimatedLines * lineHeightCm;
    const estimatedHeightPx = estimatedLines * lineHeightPx;
    
    // Add margin approximations
    const marginBottomCm = 0.25; // Approximate margin in cm
    const marginBottomPx = marginBottomCm * 37.7952755906;
    
    // Add padding for container elements
    const paddingCm = 0.2; // Approximate padding in cm
    const paddingPx = paddingCm * 37.7952755906;
    
    // Estimate total height
    const totalHeightCm = estimatedHeightCm + marginBottomCm + paddingCm;
    const totalHeightPx = estimatedHeightPx + marginBottomPx + paddingPx;
    
    // Remove the temporary container
    htmlDoc.body.removeChild(tempContainer);
    
    if (DEBUG.enabled && DEBUG.logMeasurements) {
        console.log(`Element height estimate: ${totalHeightCm.toFixed(2)}cm (${Math.round(totalHeightPx)}px) from ${charCount} characters`);
    }
    
    // Return the appropriate measurement
    return returnRawPx ? totalHeightPx : totalHeightCm;
}

/**
 * Creates a complete update item element from company data
 * @param {Object} company - The company data
 * @returns {HTMLElement} The update item element
 */
function createUpdateItemElement(company) {
    const updateItem = htmlDoc.createElement('div');
    updateItem.className = 'p6-update-item';
    updateItem.setAttribute('data-company-name', company.name);
    
    // HTML structure for the update item
    updateItem.innerHTML = `
        <div class="p6-company-logo-box">
            <img src="${company.logo}" alt="${company.name} Logo">
        </div>
        <div class="p6-fund-boxes-container">
            <div class="p6-fund-boxes-left">
                <div class="p6-fund-box">${company.fund}</div>
                <div class="p6-fund-box">Rank: <span class="p6-rank-value">${company.rank}</span></div>
            </div>
            <div class="p6-website-text">${company.website}</div>
        </div>
        <div class="p6-report-container">
            <div class="p6-report-title">
                ${company.name}
            </div>
            <div class="p6-report-body">
                ${company.report_body}
            </div>
        </div>
    `;
    
    return updateItem;
}

/**
 * Creates just the header part of a company update (logo, fund, title)
 * @param {Object} company - The company data
 * @returns {HTMLElement} The header element
 */
function createCompanyHeaderElement(company) {
    const headerItem = htmlDoc.createElement('div');
    headerItem.className = 'p6-update-item';
    headerItem.setAttribute('data-company-name', company.name);
    
    // HTML structure for just the header parts
    headerItem.innerHTML = `
        <div class="p6-company-logo-box">
            <img src="${company.logo}" alt="${company.name} Logo">
        </div>
        <div class="p6-fund-boxes-container">
            <div class="p6-fund-boxes-left">
                <div class="p6-fund-box">${company.fund}</div>
                <div class="p6-fund-box">Rank: <span class="p6-rank-value">${company.rank}</span></div>
            </div>
            <div class="p6-website-text">${company.website}</div>
        </div>
        <div class="p6-report-container">
            <div class="p6-report-title">
                ${company.name}
            </div>
            <div class="p6-report-body">
            </div>
        </div>
    `;
    
    return headerItem;
}

/**
 * Creates a continuation element for a company update (when split across pages)
 * @param {Object} company - The company data
 * @returns {HTMLElement} The continuation element
 */
function createContinuationElement(company) {
    const continuationItem = htmlDoc.createElement('div');
    continuationItem.className = 'p6-update-item p6-continuation';
    continuationItem.setAttribute('data-company-name', company.name);
    
    // HTML structure for continuation with a subtle header
    continuationItem.innerHTML = `
        <div class="p6-continuation-header">
            <div class="p6-report-title" style="margin-bottom: 0.2cm; color: #888;">
                ${company.name} (continued)
            </div>
        </div>
        <div class="p6-report-container">
            <div class="p6-report-body">
                ${company.report_body}
            </div>
        </div>
    `;
    
    return continuationItem;
}

/**
 * Creates a partial header element with just logo, fund info, and title line
 * @param {Object} company - The company data
 * @returns {Object} Object containing the partial header element and remaining text
 */
function createPartialHeaderElement(company) {
    const headerItem = htmlDoc.createElement('div');
    headerItem.className = 'p6-update-item p6-partial-header';
    headerItem.setAttribute('data-company-name', company.name);
    headerItem.setAttribute('style', 'margin-bottom: 1cm;');

    // Get the first line of report_body for the partial header
    let firstLine = '';
    let remainingText = '';
    const fullText = company.report_body;
    let cutoffIndex = 0;

    // Calculate approximate characters that fit in container width
    // Container width is approximately 18.27cm (21.59cm - 2.19cm - 1.13cm)
    // For 0.42cm font size with Clash Display font, estimate ~50-55 chars per line
    const containerWidthChars = 75; // Approximate characters that fit in one line

    // Find a good breakpoint that respects full words
    let breakIndex = Math.min(containerWidthChars, fullText.length);

    // If we're not at the end of text, find the last space before our limit
    if (breakIndex < fullText.length) {
        // Look backward from our character limit to find a space
        while (breakIndex > 0 && fullText[breakIndex] !== ' ') {
            breakIndex--;
        }

        // If we couldn't find a space (rare case with very long words)
        // or we went too far back, use a reasonable fallback
        if (breakIndex === 0 || (containerWidthChars - breakIndex) > 15) {
            // Try to find a word break using a forward search instead
            breakIndex = Math.min(containerWidthChars - 5, fullText.length);
            while (breakIndex < fullText.length && fullText[breakIndex] !== ' ') {
                breakIndex++;
            }

            // If still no good break found, just use our original estimate
            if (breakIndex >= fullText.length) {
                breakIndex = Math.min(containerWidthChars, fullText.length);
            }
        }
    }

    // Set the cutoff index to our calculated break point
    cutoffIndex = breakIndex;

    // Create the first line with ellipsis if needed
    if (cutoffIndex < fullText.length) {
        firstLine = fullText.substring(0, cutoffIndex) + '...';
    } else {
        firstLine = fullText.substring(0, cutoffIndex);
    }

    // Get the remaining text after the first part
    remainingText = fullText.substring(cutoffIndex);

    // HTML structure for header parts, title line, and first sentence of body
    headerItem.innerHTML = `
        <div class="p6-company-logo-box" style="width: 4.3cm; height: 1.09cm; display: flex; justify-content: flex-start; align-items: center; object-fit: contain;">
            <img src="${company.logo}" alt="${company.name} Logo" style="max-width: 100%; max-height: 100%; width: auto; height: auto; object-fit: contain;">
        </div>
        <div class="p6-fund-boxes-container" style="margin-top: 0.28cm; display: flex; gap: 0.53cm; justify-content: space-between; width: 100%;">
            <div class="p6-fund-boxes-left" style="display: flex; gap: 0.53cm;">
                <div class="p6-fund-box" style="min-width: 1.5cm; height: 0.6cm; background: #B2D1B2; border-radius: 4px; display: flex; justify-content: center; align-items: center; font-family: 'Clash Display', sans-serif; font-size: 0.32cm; padding-left: 0.2cm; padding-right: 0.2cm; font-weight: 500; white-space: nowrap;">${company.fund}</div>
                <div class="p6-fund-box" style="min-width: 1.5cm; height: 0.6cm; background: #B2D1B2; border-radius: 4px; display: flex; justify-content: center; align-items: center; font-family: 'Clash Display', sans-serif; font-size: 0.32cm; padding-left: 0.2cm; padding-right: 0.2cm; font-weight: 500; white-space: nowrap;">Rank: <span class="p6-rank-value" style="font-weight: 700; margin-left: 0.14cm;">${company.rank}</span></div>
            </div>
            <div class="p6-website-text" style="color: #5CD468; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 400; line-height: 0.64cm; word-wrap: break-word; align-self: center;">${company.website}</div>
        </div>
        <div class="p6-report-container">
            <div class="p6-report-title" style="color: black; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 600; line-height: 0.64cm; word-wrap: break-word; margin-top: 0.28cm;">
                ${company.name}
            </div>
            <div class="p6-report-body" style="color: black; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 400; line-height: 0.64cm; word-wrap: break-word; margin-top: 0.28cm;">
                ${firstLine}
            </div>
        </div>
    `;

    return {
        element: headerItem,
        remainingText: remainingText
    };
}

/**
 * Splits a company report body text to fit in available space
 * @param {Object} company - The company data
 * @param {number} availableHeight - Available height in cm
 * @returns {Object} First part item, second part item, and first part height
 */
function splitReportBodyText(company, availableHeight) {
    // Start with the full text and split by characters for more precise control
    const fullText = company.report_body;
    
    // For partial headers: identify the first line to exclude from continuation
    let firstLineEndIndex = -1;
    const endMarkers = ['. ', '? ', '! '];
    
    // Find the first sentence end
    for (const marker of endMarkers) {
        const index = fullText.indexOf(marker);
        if (index !== -1 && (firstLineEndIndex === -1 || index < firstLineEndIndex)) {
            firstLineEndIndex = index + marker.length;
        }
    }
    
    // If no sentence end found or sentence too long, cap at reasonable length
    if (firstLineEndIndex === -1 || firstLineEndIndex > 80) {
        firstLineEndIndex = Math.min(80, fullText.length);
    }
    
    // Binary search approach to find optimal split point based on characters
    let minChars = 1;
    let maxChars = fullText.length;
    let midChars = Math.floor((minChars + maxChars) / 2);
    let bestSplit = midChars;
    let firstPartHeight = 0;
    
    // Create elements for measurement
    const firstPartItem = createCompanyHeaderElement(company);
    const firstPartBody = firstPartItem.querySelector('.p6-report-body');
    
    // For debugging - track iterations and height calculations
    let debugInfo = [];
    
    // Binary search for optimal text split point
    for (let i = 0; i < 10; i++) { // Limit iterations to prevent infinite loops
        // Try with current character count
        // Find the nearest word boundary to avoid splitting mid-word
        let splitIndex = midChars;
        
        // Look for the nearest space character
        while (splitIndex > 0 && fullText[splitIndex] !== ' ') {
            splitIndex--;
        }
        
        // If we went all the way back to the beginning, just use the original midpoint
        if (splitIndex === 0) {
            splitIndex = midChars;
        }
        
        const testText = fullText.substring(0, splitIndex);
        firstPartBody.textContent = testText;
        
        // Measure the height of this text with direct DOM measurement
        const totalHeight = calculateElementHeight(firstPartItem);
        
        // Store debug info if in debug mode
        if (DEBUG.enabled) {
            debugInfo.push({
                iteration: i, 
                charactersUsed: splitIndex,
                height: totalHeight,
                heightRawPx: calculateElementHeight(firstPartItem, true),
                fits: totalHeight <= availableHeight
            });
        }
        
        if (totalHeight <= availableHeight) {
            // This fits, try using more characters
            minChars = midChars;
            bestSplit = splitIndex;
            firstPartHeight = totalHeight;
        } else {
            // Too big, try using fewer characters
            maxChars = midChars;
        }
        
        // Update midpoint for next iteration
        midChars = Math.floor((minChars + maxChars) / 2);
        
        // If we're not making progress, stop
        if (Math.abs(bestSplit - midChars) < 10) break;
    }
    
    // Log debug info if in debug mode
    if (DEBUG.enabled) {
        console.log('Text split optimization for ' + company.name + ':');
        console.table(debugInfo);
    }
    
    // Find a clean break point near our best split (at a sentence ending if possible)
    // Look for period, question mark, or exclamation mark followed by space
    let cleanBreakPoint = bestSplit;
    
    // Search backward from bestSplit for a good break point, but don't go too far back
    const searchRange = Math.min(100, bestSplit); // Look back at most 100 characters
    const textToSearch = fullText.substring(bestSplit - searchRange, bestSplit);
    
    for (const marker of endMarkers) {
        const lastIndex = textToSearch.lastIndexOf(marker);
        if (lastIndex !== -1) {
            cleanBreakPoint = bestSplit - searchRange + lastIndex + marker.length;
            break;
        }
    }
    
    // Create the final first part with optimal text amount
    const firstPartText = fullText.substring(0, cleanBreakPoint) + '...';
    firstPartBody.textContent = firstPartText;
    
    // Create the second part with remaining text - skipping the first line
    const secondPartItem = createContinuationElement(company);
    const secondPartBody = secondPartItem.querySelector('.p6-report-body');
    const continuationText = fullText.substring(Math.max(cleanBreakPoint, firstLineEndIndex));
    secondPartBody.textContent = continuationText;
    
    return {
        firstPartItem: firstPartItem,
        secondPartItem: secondPartItem,
        firstPartHeight: firstPartHeight
    };
}


function updatePages(data) {
    // Update Page 1
    htmlDoc.getElementById('report-title').textContent = data.report_title;
    htmlDoc.getElementById('fund-name').textContent = data.fund_name;
    htmlDoc.getElementById('reporting-period').textContent = data.reporting_period;
    htmlDoc.getElementById('company-name').textContent = data.company_name;
    
    // Update Page 2
    htmlDoc.getElementById('executive-summary-title').textContent = data.executive_summary_title;
    
    // Update paragraphs
    const execParagraphContainer = htmlDoc.getElementById('exec-paragraph');
    if (execParagraphContainer && data.paragraphs && data.paragraphs.length > 0) {
        // Clear existing content
        execParagraphContainer.innerHTML = '';
        
        // Loop through paragraphs and create a div for each
        data.paragraphs.forEach((paragraph, index) => {
            const paragraphDiv = htmlDoc.createElement('div');
            paragraphDiv.style.marginTop = '0.81cm';
            paragraphDiv.id = `exec-paragraph-${index + 1}`;
            
            const paragraphElement = htmlDoc.createElement('p');
            paragraphElement.style.color = 'black';
            paragraphElement.style.fontSize = '0.42cm';
            paragraphElement.style.fontFamily = "'Clash Display', sans-serif";
            paragraphElement.style.fontWeight = '400';
            paragraphElement.style.lineHeight = '0.63cm';
            paragraphElement.style.wordWrap = 'break-word';
            paragraphElement.textContent = paragraph;
            
            paragraphDiv.appendChild(paragraphElement);
            execParagraphContainer.appendChild(paragraphDiv);
        });
    } 

    return htmlDoc.documentElement.outerHTML;
} 

const data = $input.data

// Call functions and get the updated HTML
const updatedHtml = updatePages(data);
const finalHtml = initializePortfolioTemplate(data);

return finalHtml;
