<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="pdf-navigation" content="enabled">
    <meta name="pdf-bookmarks" content="enabled">
    <title>PDF Navigation Test</title>
    <link rel="stylesheet" href="https://api.fontshare.com/v2/css?f[]=clash-display@400,500,600&display=swap">
    <style>
        @media print {
            @page { 
                margin: 0; 
                size: letter; 
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            body { 
                margin: 0; 
                padding: 0; 
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .page { 
                page-break-after: always; 
                height: 27.94cm;
                width: 21.59cm;
            }
            .toc-page { 
                page-break-after: always; 
            }
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: 'Clash Display', Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .page {
            width: 21.59cm;
            height: 27.94cm;
            background: white;
            margin: 0 auto 20px;
            position: relative;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .toc-page {
            padding: 2cm;
        }
        
        .content-page {
            padding: 3cm 2cm;
        }
        
        /* TOC Styles */
        .toc-title {
            font-size: 2em;
            font-weight: 800;
            margin-bottom: 2cm;
            color: #333;
        }
        
        .toc-section {
            margin-bottom: 1.5cm;
        }
        
        .toc-section-title {
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 0.5cm;
            color: #666;
            border-bottom: 2px solid #eee;
            padding-bottom: 0.2cm;
        }
        
        /* Different TOC link approaches */
        .toc-simple {
            display: block;
            margin: 0.3cm 0;
            text-decoration: none;
            color: #333;
            padding: 0.2cm 0;
            border-bottom: 1px dotted #ccc;
        }
        
        .toc-simple:hover {
            background-color: #f5f5f5;
        }
        
        .toc-flex {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0.3cm 0;
            text-decoration: none;
            color: #333;
            padding: 0.2cm 0;
            border-bottom: 1px dotted #ccc;
        }
        
        .toc-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1cm 0;
        }
        
        .toc-table td {
            padding: 0.3cm 0;
            border-bottom: 1px dotted #ccc;
        }
        
        .toc-table a {
            text-decoration: none;
            color: #333;
        }
        
        .page-number {
            font-weight: 700;
            text-align: right;
            min-width: 2cm;
        }
        
        /* Content page styles */
        .page-title {
            font-size: 1.8em;
            font-weight: 700;
            margin-bottom: 1cm;
            color: #333;
        }
        
        .company-name {
            font-size: 1.4em;
            font-weight: 600;
            color: #0066cc;
            margin-bottom: 0.5cm;
        }
        
        .content-text {
            line-height: 1.6;
            color: #444;
            margin-bottom: 1cm;
        }
        
        /* Anchor styles - multiple approaches */
        .anchor-invisible {
            position: absolute;
            top: 0;
            left: 0;
            visibility: hidden;
            height: 1px;
            width: 1px;
        }
        
        .anchor-transparent {
            color: transparent;
            font-size: 1px;
            line-height: 1px;
        }
        
        .anchor-block {
            display: block;
            height: 0;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <!-- TABLE OF CONTENTS PAGE -->
    <div class="page toc-page">
        <h1 class="toc-title">Table of Contents - PDF Navigation Test</h1>
        
        <!-- Method 1: Simple Links -->
        <div class="toc-section">
            <h2 class="toc-section-title">Method 1: Simple Block Links</h2>
            <a href="#page-1" class="toc-simple">Page 1: Company Alpha (Simple Link)</a>
            <a href="#page-2" class="toc-simple">Page 2: Company Beta (Simple Link)</a>
            <a href="#page-3" class="toc-simple">Page 3: Company Gamma (Simple Link)</a>
        </div>
        
        <!-- Method 2: Flex Layout Links -->
        <div class="toc-section">
            <h2 class="toc-section-title">Method 2: Flex Layout Links</h2>
            <a href="#page-1" class="toc-flex">
                <span>Page 1: Company Alpha</span>
                <span class="page-number">1</span>
            </a>
            <a href="#page-2" class="toc-flex">
                <span>Page 2: Company Beta</span>
                <span class="page-number">2</span>
            </a>
            <a href="#page-3" class="toc-flex">
                <span>Page 3: Company Gamma</span>
                <span class="page-number">3</span>
            </a>
        </div>
        
        <!-- Method 3: Table with Links -->
        <div class="toc-section">
            <h2 class="toc-section-title">Method 3: Table Structure</h2>
            <table class="toc-table">
                <tr>
                    <td><a href="#page-1">Page 1: Company Alpha</a></td>
                    <td class="page-number"><a href="#page-1">1</a></td>
                </tr>
                <tr>
                    <td><a href="#page-2">Page 2: Company Beta</a></td>
                    <td class="page-number"><a href="#page-2">2</a></td>
                </tr>
                <tr>
                    <td><a href="#page-3">Page 3: Company Gamma</a></td>
                    <td class="page-number"><a href="#page-3">3</a></td>
                </tr>
            </table>
        </div>
        
        <!-- Method 4: Separate Company and Page Links -->
        <div class="toc-section">
            <h2 class="toc-section-title">Method 4: Separate Links</h2>
            <div style="display: flex; justify-content: space-between; margin: 0.3cm 0; border-bottom: 1px dotted #ccc; padding: 0.2cm 0;">
                <a href="#page-1" style="text-decoration: none; color: #333;">Company Alpha</a>
                <a href="#page-1" style="text-decoration: none; color: #333; font-weight: 700;">1</a>
            </div>
            <div style="display: flex; justify-content: space-between; margin: 0.3cm 0; border-bottom: 1px dotted #ccc; padding: 0.2cm 0;">
                <a href="#page-2" style="text-decoration: none; color: #333;">Company Beta</a>
                <a href="#page-2" style="text-decoration: none; color: #333; font-weight: 700;">2</a>
            </div>
            <div style="display: flex; justify-content: space-between; margin: 0.3cm 0; border-bottom: 1px dotted #ccc; padding: 0.2cm 0;">
                <a href="#page-3" style="text-decoration: none; color: #333;">Company Gamma</a>
                <a href="#page-3" style="text-decoration: none; color: #333; font-weight: 700;">3</a>
            </div>
        </div>
    </div>
    
    <!-- PAGE 1 -->
    <div class="page content-page" id="page-1">
        <!-- Multiple anchor approaches -->
        <a name="page-1"></a>
        <a name="page-1-alt" class="anchor-invisible"></a>
        <span id="anchor-page-1" class="anchor-transparent">page-1</span>
        <div class="anchor-block"><a name="page-1-block"></a></div>
        
        <h1 class="page-title">Page 1</h1>
        <h2 class="company-name">Company Alpha</h2>
        <div class="content-text">
            <p>This is the content for Company Alpha. This page tests multiple anchor approaches to ensure PDF navigation works correctly.</p>
            <p>The anchors used on this page are:</p>
            <ul>
                <li><code>&lt;a name="page-1"&gt;&lt;/a&gt;</code> - Simple name anchor</li>
                <li><code>&lt;a name="page-1-alt" class="anchor-invisible"&gt;&lt;/a&gt;</code> - Invisible positioned anchor</li>
                <li><code>&lt;span id="anchor-page-1" class="anchor-transparent"&gt;page-1&lt;/span&gt;</code> - Transparent span with ID</li>
                <li><code>&lt;div class="anchor-block"&gt;&lt;a name="page-1-block"&gt;&lt;/a&gt;&lt;/div&gt;</code> - Block-level anchor</li>
            </ul>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.</p>
        </div>
    </div>
    
    <!-- PAGE 2 -->
    <div class="page content-page" id="page-2">
        <!-- Multiple anchor approaches -->
        <a name="page-2"></a>
        <a name="page-2-alt" class="anchor-invisible"></a>
        <span id="anchor-page-2" class="anchor-transparent">page-2</span>
        <div class="anchor-block"><a name="page-2-block"></a></div>
        
        <h1 class="page-title">Page 2</h1>
        <h2 class="company-name">Company Beta</h2>
        <div class="content-text">
            <p>This is the content for Company Beta. This page also tests multiple anchor approaches.</p>
            <p>Key features of this test:</p>
            <ul>
                <li>Multiple TOC link styles on the first page</li>
                <li>Multiple anchor types on each content page</li>
                <li>Print-optimized CSS</li>
                <li>PDF-specific meta tags</li>
            </ul>
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        </div>
    </div>
    
    <!-- PAGE 3 -->
    <div class="page content-page" id="page-3">
        <!-- Multiple anchor approaches -->
        <a name="page-3"></a>
        <a name="page-3-alt" class="anchor-invisible"></a>
        <span id="anchor-page-3" class="anchor-transparent">page-3</span>
        <div class="anchor-block"><a name="page-3-block"></a></div>
        
        <h1 class="page-title">Page 3</h1>
        <h2 class="company-name">Company Gamma</h2>
        <div class="content-text">
            <p>This is the content for Company Gamma. Final test page with all anchor approaches.</p>
            <p>Testing instructions:</p>
            <ol>
                <li>Open this HTML file in a web browser</li>
                <li>Test all TOC links - they should work in the browser</li>
                <li>Print to PDF using your preferred method</li>
                <li>Test the links in the generated PDF</li>
                <li>Note which TOC method works best in your PDF</li>
            </ol>
            <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
        </div>
    </div>
</body>
</html>
