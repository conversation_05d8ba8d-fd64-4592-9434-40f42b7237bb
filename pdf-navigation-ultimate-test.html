<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Ultimate PDF Navigation Test</title>
    <style>
        @media print {
            @page { margin: 0; size: letter; }
            body { margin: 0; padding: 0; }
            .page { page-break-after: always; height: 27.94cm; width: 21.59cm; }
        }
        
        body { margin: 0; font-family: Arial, sans-serif; background: #f0f0f0; }
        .page { width: 21.59cm; height: 27.94cm; background: white; margin: 0 auto 20px; padding: 2cm; box-sizing: border-box; }
        .toc-link { display: block; margin: 10px 0; padding: 10px; background: #f9f9f9; text-decoration: none; color: #333; border: 1px solid #ddd; }
        .toc-link:hover { background: #e9e9e9; }
        .page-content { margin-top: 2cm; }
        h1 { color: #333; border-bottom: 2px solid #333; padding-bottom: 10px; }
        h2 { color: #666; }
    </style>
</head>
<body>
    <!-- TOC PAGE -->
    <div class="page">
        <h1>Ultimate PDF Navigation Test</h1>
        <p><strong>Instructions:</strong> Click any link below. It should jump to the corresponding page in both browser and PDF.</p>
        
        <h2>Test Links (Try each one):</h2>
        
        <!-- Ultra-simple approach -->
        <a href="#page1" class="toc-link">🔗 Page 1: Ultra Simple Anchor (name="page1")</a>
        <a href="#page2" class="toc-link">🔗 Page 2: ID-based Anchor (id="page2")</a>
        <a href="#page3" class="toc-link">🔗 Page 3: Both Name and ID</a>
        <a href="#page4" class="toc-link">🔗 Page 4: Visible Anchor Text</a>
        <a href="#page5" class="toc-link">🔗 Page 5: Header as Anchor</a>
        
        <div style="margin-top: 2cm; padding: 1cm; background: #fff3cd; border: 1px solid #ffeaa7;">
            <h3>Testing Notes:</h3>
            <ul>
                <li>Test in browser first - all links should work</li>
                <li>Print to PDF and test again</li>
                <li>Note which approach works in your PDF tool</li>
                <li>Common PDF tools: Chrome Print, Firefox Print, Puppeteer, wkhtmltopdf</li>
            </ul>
        </div>
    </div>
    
    <!-- PAGE 1: Ultra Simple -->
    <div class="page">
        <a name="page1"></a>
        <h1>Page 1: Ultra Simple Anchor</h1>
        <h2>Anchor Method: &lt;a name="page1"&gt;&lt;/a&gt;</h2>
        <div class="page-content">
            <p>This page uses the most basic anchor possible: <code>&lt;a name="page1"&gt;&lt;/a&gt;</code></p>
            <p>This is the most widely supported method across different PDF generators.</p>
            <p><strong>Pros:</strong> Maximum compatibility, simple, no CSS interference</p>
            <p><strong>Cons:</strong> Basic, no styling options</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        </div>
    </div>
    
    <!-- PAGE 2: ID-based -->
    <div class="page" id="page2">
        <h1>Page 2: ID-based Anchor</h1>
        <h2>Anchor Method: &lt;div id="page2"&gt;</h2>
        <div class="page-content">
            <p>This page uses an ID on the page container: <code>&lt;div id="page2"&gt;</code></p>
            <p>Modern approach, works well in browsers but may have issues in some PDF tools.</p>
            <p><strong>Pros:</strong> Clean, modern, can be styled</p>
            <p><strong>Cons:</strong> Some PDF tools don't support ID-based navigation</p>
            <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        </div>
    </div>
    
    <!-- PAGE 3: Both Name and ID -->
    <div class="page" id="page3">
        <a name="page3"></a>
        <h1>Page 3: Both Name and ID</h1>
        <h2>Anchor Method: &lt;a name="page3"&gt;&lt;/a&gt; + &lt;div id="page3"&gt;</h2>
        <div class="page-content">
            <p>This page uses both methods for maximum compatibility.</p>
            <p>Combines the reliability of name anchors with the flexibility of ID-based targeting.</p>
            <p><strong>Pros:</strong> Maximum compatibility, fallback options</p>
            <p><strong>Cons:</strong> Slightly more code</p>
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
        </div>
    </div>
    
    <!-- PAGE 4: Visible Anchor -->
    <div class="page">
        <h1>Page 4: Visible Anchor Text</h1>
        <h2>Anchor Method: <a name="page4">Visible Anchor</a></h2>
        <div class="page-content">
            <p>This page has a visible anchor with actual text content.</p>
            <p>Some PDF tools work better when anchors have visible content.</p>
            <p><strong>Pros:</strong> Visible to users, some PDF tools prefer this</p>
            <p><strong>Cons:</strong> May affect layout, visible anchor text</p>
            <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        </div>
    </div>
    
    <!-- PAGE 5: Header as Anchor -->
    <div class="page">
        <h1><a name="page5">Page 5: Header as Anchor</a></h1>
        <h2>Anchor Method: &lt;h1&gt;&lt;a name="page5"&gt;Header Text&lt;/a&gt;&lt;/h1&gt;</h2>
        <div class="page-content">
            <p>This page uses the header itself as the anchor target.</p>
            <p>Natural approach that works well with document structure.</p>
            <p><strong>Pros:</strong> Semantic, natural document flow</p>
            <p><strong>Cons:</strong> Anchor is part of header styling</p>
            <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>
        </div>
    </div>
    
    <!-- RESULTS PAGE -->
    <div class="page">
        <h1>Test Results</h1>
        <h2>Record Your Results Here:</h2>
        <div style="background: #f8f9fa; padding: 1cm; border: 1px solid #dee2e6;">
            <h3>Browser Test Results:</h3>
            <p>□ Page 1 (Ultra Simple) - Works: ___</p>
            <p>□ Page 2 (ID-based) - Works: ___</p>
            <p>□ Page 3 (Both) - Works: ___</p>
            <p>□ Page 4 (Visible) - Works: ___</p>
            <p>□ Page 5 (Header) - Works: ___</p>
            
            <h3>PDF Test Results:</h3>
            <p>PDF Tool Used: ________________</p>
            <p>□ Page 1 (Ultra Simple) - Works: ___</p>
            <p>□ Page 2 (ID-based) - Works: ___</p>
            <p>□ Page 3 (Both) - Works: ___</p>
            <p>□ Page 4 (Visible) - Works: ___</p>
            <p>□ Page 5 (Header) - Works: ___</p>
            
            <h3>Recommendation:</h3>
            <p>Use the method that worked best in your PDF tool for your actual TOC.</p>
        </div>
        
        <div style="margin-top: 1cm; padding: 1cm; background: #d1ecf1; border: 1px solid #bee5eb;">
            <h3>Common PDF Tools and Their Anchor Support:</h3>
            <ul>
                <li><strong>Chrome Print to PDF:</strong> Usually supports all methods</li>
                <li><strong>Firefox Print to PDF:</strong> Good support for name anchors</li>
                <li><strong>Puppeteer:</strong> Excellent support for all methods</li>
                <li><strong>wkhtmltopdf:</strong> Best with simple name anchors</li>
                <li><strong>Prince XML:</strong> Excellent support</li>
                <li><strong>WeasyPrint:</strong> Good support for name anchors</li>
            </ul>
        </div>
    </div>
</body>
</html>
