<svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2020_2)">
<rect width="612" height="792" fill="white"/>
<g opacity="0.5" filter="url(#filter0_f_2020_2)">
<ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint0_linear_2020_2)"/>
</g>
</g>
<defs>
<filter id="filter0_f_2020_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2020_2"/>
</filter>
<linearGradient id="paint0_linear_2020_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509" gradientUnits="userSpaceOnUse">
<stop stop-color="#D1F15E"/>
<stop offset="0.94" stop-color="#70E9EF"/>
</linearGradient>
<clipPath id="clip0_2020_2">
<rect width="612" height="792" fill="white"/>
</clipPath>
</defs>
</svg>
