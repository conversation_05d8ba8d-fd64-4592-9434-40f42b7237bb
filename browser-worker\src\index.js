import puppeteer from "@cloudflare/puppeteer";
import { Hono } from 'hono';

const app = new Hono();

app.get('/', (c) => {
  return c.json({ message: 'Hello, <PERSON>gar!' });
});

// Define POST route for PDF generation
app.post('/pdf', async (c) => {
  // Get HTML content from request body
  let document;
  try {
    const body = await c.req.json();
    document = body.html || "";
    
    if (!document) {
      return c.json({ error: "Please provide HTML content in the request body's 'html' field" }, 400);
    }
  } catch (error) {
    return c.json({ error: "Invalid JSON in request body. Please provide a JSON object with an 'html' field." }, 400);
  }

//   return c.html(document);

  let pdf;
  let page;
  let browser;
  let debug;
  
  try {
    browser = await puppeteer.launch(c.env.BROWSER);
	debug = "step 1";
    page = await browser.newPage();
	debug = "step 2";
    // Step 2: Send HTML and CSS to our browser
    await page.setContent(document);
	debug = "step 3";
    // Step 3: Generate and return PDF
    // pdf = await page.createPDFStream();
    pdf = await page.pdf();
	// pdf = await page.createPDFStream({
	// 	format: 'A4',
	// 	printBackground: true, // Ensures background colors and images are rendered
	// 	preferCSSPageSize: true, // Respects CSS page size settings
	// 	// displayHeaderFooter: true,
	//   });
	debug = "step 4";
  } catch (error) {
	return c.json({ debug: debug });

    return c.json({ error: "Error generating PDF" }, 500);
  } finally {
    if (page) await page.close();
    if (browser) await browser.close();
  }

  return c.json({ debug: debug });

  return c.response(pdf, 200, { 
    "Content-Type": "application/pdf",
    "Content-Disposition": "attachment; filename=report.pdf"
  });
});


export default {
  fetch: app.fetch,
};