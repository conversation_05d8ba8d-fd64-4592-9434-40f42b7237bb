function generateQuarterlyReport(companies) {
    // Handle both array and single company object
    const companyList = Array.isArray(companies) ? companies : [companies];

    if (!companyList.length || typeof companyList[0] !== 'object') {
        console.log('Input companies data is not valid. Returning null.');
        return null;
    }

    // Get fund name from first company if available
    const fundNumber = companyList[0].fund_number || "";
    const quarter = companyList[0].quarter || "";

    // Calculate aggregate metrics using metrics array when available
    const totalUnrealizedValue = companyList.reduce((sum, company) => {
        // Check if unrealized_value exists in metrics array
        const metricValue = company.metrics?.find(m => m.key === 'unrealized_value')?.value;
        return sum + (metricValue || parseFloat(company.unrealized_value) || 0);
    }, 0);
    
    const totalDistributions = companyList.reduce((sum, company) => {
        const metricValue = company.metrics?.find(m => m.key === 'distributions_to_date')?.value;
        return sum + (metricValue || parseFloat(company.distributions_to_date) || 0);
    }, 0);
    
    const totalCapital_called_ytd = companyList.reduce((sum, company) => {
        const metricValue = company.metrics?.find(m => m.key === 'capital_called_ytd')?.value;
        return sum + (metricValue || parseFloat(company.capital_called_ytd) || 0);
    }, 0);

    const totalCapitalDeployed = companyList.reduce((sum, company) => {
        const metricValue = company.metrics?.find(m => m.key === 'capital_deployed_to_date')?.value;
        return sum + (metricValue || parseFloat(company.capital_deployed_to_date) || 0);
    }, 0);

    // Calculate weighted averages for percentage metrics
    const totalNetValue = companyList.reduce((sum, company) => {
        const unrealizedValue = company.metrics?.find(m => m.key === 'unrealized_value')?.value || parseFloat(company.unrealized_value) || 0;
        const distributions = company.metrics?.find(m => m.key === 'distributions_to_date')?.value || parseFloat(company.distributions_to_date) || 0;
        return sum + (unrealizedValue + distributions);
    }, 0);

    // Weight IRR by each company's proportion of total value
    const weightedNetIRR = companyList.reduce((sum, company) => {
        const unrealizedValue = company.metrics?.find(m => m.key === 'unrealized_value')?.value || parseFloat(company.unrealized_value) || 0;
        const distributions = company.metrics?.find(m => m.key === 'distributions_to_date')?.value || parseFloat(company.distributions_to_date) || 0;
        const companyTotalValue = unrealizedValue + distributions;
        const weight = totalNetValue > 0 ? companyTotalValue / totalNetValue : 0;
        const netIRR = company.metrics?.find(m => m.key === 'net_irr')?.value || parseFloat(company.net_irr) || 0;
        return sum + weight * netIRR;
    }, 0);

    const weightedGrossIRR = companyList.reduce((sum, company) => {
        const unrealizedValue = company.metrics?.find(m => m.key === 'unrealized_value')?.value || parseFloat(company.unrealized_value) || 0;
        const distributions = company.metrics?.find(m => m.key === 'distributions_to_date')?.value || parseFloat(company.distributions_to_date) || 0;
        const companyTotalValue = unrealizedValue + distributions;
        const weight = totalNetValue > 0 ? companyTotalValue / totalNetValue : 0;
        const grossIRR = company.metrics?.find(m => m.key === 'gross_irr')?.value || parseFloat(company.gross_irr) || 0;
        return sum + weight * grossIRR;
    }, 0);

    // Calculate portfolio-wide DPI and RVPI
    const portfolioDPI = totalCapitalDeployed > 0 ? totalDistributions / totalCapitalDeployed : 0;
    const portfolioRVPI = totalCapitalDeployed > 0 ? totalUnrealizedValue / totalCapitalDeployed : 0;

    // Calculate total quarterly disbursements
    const totalQuarterlyDisbursements = companyList.reduce((sum, company) => {
        const metricValue = company.metrics?.find(m => m.key === 'quarterly_disbursements')?.value;
        return sum + (metricValue || parseFloat(company.quarterly_disbursements) || 0);
    }, 0);

    // Separate companies based on current_stage
    const activeCompanies = companyList.filter(company => 
        company.current_stage === 'Active' || !company.current_stage);
    
    const exitCompanies = companyList.filter(company => 
        company.current_stage === 'Exit');
    
    const writeOffCompanies = companyList.filter(company => 
        company.current_stage === 'Write Off');

    // Create company objects with required schema
    const formatCompany = company => ({
        name: company.name || "",
        logo: company.logo_URL || "",
        fund: company.fund_number || "",
        rank: company.rank || "",
        website: company.url || "",
        report_body: company.investment_overview || ""
    });

    return JSON.stringify({
        report_title: `${quarter} Performance Report`,
        fund_name: fundNumber,
        reporting_period: quarter,
        company_name: "RENDYR CAPITAL",

        executive_summary_title: "Executive Summary",
        paragraphs: $var.overview,

        net_irr: (weightedNetIRR.toFixed(2)),
        gross_irr: (weightedGrossIRR.toFixed(2)),
        dpi: (portfolioDPI.toFixed(2)),
        rvpi: (portfolioRVPI.toFixed(2)),

        total_capital_deployed: formatUsCurrency(companyList[0].capital_deployed_to_date) || 0,
        distributions_to_date: formatUsCurrency(totalDistributions),
        unrealized_value: formatUsCurrency(totalUnrealizedValue),
        portfolio_company_count: companyList.length,
        capital_called_ytd: formatUsCurrency(totalCapital_called_ytd) || 0,
        quarter_disbursements: formatUsCurrency(totalQuarterlyDisbursements.toFixed(2)),
        quarter_number: $var.q_without_year,

        companies: activeCompanies.map(formatCompany),
        exit_companies: exitCompanies.map(formatCompany),
        write_off_companies: writeOffCompanies.map(formatCompany)
    });
}

/**
 * Formats a number to US currency format with commas and abbreviations
 * Uses K for thousands, M for millions, B for billions
 * @param {number} value - The number to format
 * @return {string} - The formatted currency string
 */
function formatUsCurrency(value) {
    // Handle non-numeric values
    if (typeof value !== 'number' || isNaN(value)) {
        return '$0.00';
    }

    // For values 1 billion or greater
    if (value >= 1000000000) {
        const billions = value / 1000000000;

        // For even billions, show whole number
        if (billions === Math.floor(billions)) {
            return `$${billions}B`;
        }
        // Otherwise show with one decimal place
        else {
            return `$${billions.toFixed(1)}B`;
        }
    }

    // For values 1 million or greater
    if (value >= 1000000) {
        const millions = value / 1000000;

        // For even millions, show whole number
        if (millions === Math.floor(millions)) {
            return `$${millions}M`;
        }
        // Otherwise show with one decimal place
        else {
            return `$${millions.toFixed(1)}M`;
        }
    }

    // For values 1 thousand or greater
    if (value >= 1000) {
        const thousands = value / 1000;

        // For even thousands, show whole number
        if (thousands === Math.floor(thousands)) {
            return `$${thousands}K`;
        }
        // Otherwise show with one decimal place
        else {
            return `$${thousands.toFixed(1)}K`;
        }
    }

    // For values less than 1000, just add dollar sign
    return '$' + value.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    });
}

return generateQuarterlyReport($var.companies);