const { JSDOM } = await import("npm:jsdom");
const htmlToPdfmake = (await import("npm:html-to-pdfmake")).default;
const PdfPrinterModule = await import("npm:pdfmake");
// Dynamically import Buffer from Node.js compatibility layer
const { Buffer } = await import("node:buffer");

// --- 1. Input Validation ---
const htmlContent = $var.data1?.content;
if (!htmlContent || typeof htmlContent !== 'string' || htmlContent.trim() === '') {
    console.error("HTML content ($var.data1.content) is missing, invalid, or empty.");
    throw new Error("Input HTML content ($var.data1.content) is required.");
}

// --- 2. Font Setup ---
// Using built-in Helvetica and custom fonts
const fonts = {
    Helvetica: {
        normal: 'Helvetica',
        bold: 'Helvetica-Bold',
        italics: 'Helvetica-Oblique',
        bolditalics: 'Helvetica-BoldOblique'
    },
    Montserrat: {
        normal: 'Montserrat-Regular',
        bold: 'Montserrat-Bold',
        italics: 'Montserrat-Italic',
        bolditalics: 'Montserrat-BoldItalic'
    },
    ClashDisplay: {
        normal: 'ClashDisplay-Regular',
        bold: 'ClashDisplay-Bold',
        medium: 'ClashDisplay-Medium',
        semibold: 'ClashDisplay-Semibold'
    }
};

// --- 3. Initialise Printer ---
console.log("Initializing PdfPrinter with fonts...");
// Robustly get PdfPrinter class, handling different module export styles
const PdfPrinter = PdfPrinterModule.PdfPrinter || PdfPrinterModule.default;
if (!PdfPrinter) {
    throw new Error("Could not find PdfPrinter class in the imported pdfmake module.");
}
const printer = new PdfPrinter(fonts);
console.log("PdfPrinter initialized.");

// --- 4. Parse HTML into PDF content ---
let pdfmakeContent;
try {
    console.log("Creating JSDOM instance...");
    const dom = new JSDOM(htmlContent);
    console.log("Converting HTML to pdfmake format...");
    // Use the full HTML content as html-to-pdfmake should handle it
    pdfmakeContent = htmlToPdfmake(htmlContent, { window: dom.window });
    console.log("HTML converted successfully.");
} catch (e) {
    console.error("Error during JSDOM creation or HTML conversion:", e);
    throw new Error(`Failed to parse HTML or convert to pdfmake format: ${e.message}`);
}

// --- 5. Inject table and page-break options ---
console.log("Applying content rules (table/page-break)...");
try {
    const applyRules = (node) => {
        if (!node) return;
        // Rule for tables
        if (node.table) {
            node.keepWithHeaderRows = node.keepWithHeaderRows ?? 1;
            node.layout = { ...(node.layout || {}), dontBreakRows: true };
        }
        // Rule for page breaks
        if (node.style && node.style.pageBreakBefore === 'always') {
            node.pageBreak = 'before';
        }
        // Recursively apply rules
        if (Array.isArray(node.content)) {
           node.content.forEach(applyRules);
        } else if (Array.isArray(node.columns)) {
           node.columns.forEach(applyRules);
        } else if (node.stack && Array.isArray(node.stack)) {
           node.stack.forEach(applyRules);
        }
    };

    if (Array.isArray(pdfmakeContent)) {
        pdfmakeContent.forEach(applyRules);
    } else if (pdfmakeContent && typeof pdfmakeContent === 'object') {
        applyRules(pdfmakeContent);
    } else {
        console.warn("pdfmakeContent is not an array or object, skipping rule application.");
    }
    console.log("Content rules applied.");
} catch (e) {
    console.warn("Warning: Error applying rules to pdfmake content:", e);
}

// --- 6. Build document definition ---
console.log("Building document definition...");
const docDefinition = {
    pageSize: 'LETTER',
    pageOrientation: 'portrait',
    // pageMargins: [36, 36, 36, 36],
    content: pdfmakeContent,
    defaultStyle: { font: 'Helvetica' }, // Default font remains Helvetica
    fonts: {
        Helvetica: { fallback: true },
        Montserrat: { fallback: false },
        ClashDisplay: { fallback: false }
    }
};

// --- 7. Generate PDF (Promise-based stream handling using Buffer.concat) ---
console.log("Starting PDF generation...");
return await new Promise((resolve, reject) => {
    try {
        const pdfDoc = printer.createPdfKitDocument(docDefinition);
        const chunks = [];

        pdfDoc.on('data', chunk => {
            // Assuming chunk is compatible with Buffer (Buffer or Uint8Array)
            chunks.push(chunk);
        });

        pdfDoc.on('end', () => {
            console.log(`PDF stream ended. Processing ${chunks.length} chunks.`);
            try {
                console.log("Concatenating chunks using Buffer.concat...");
                // Use Buffer.concat, requires Buffer to be imported
                const finalBuffer = Buffer.concat(chunks);
                console.log("Encoding Buffer to Base64...");
                const base64Pdf = finalBuffer.toString('base64');
                console.log("PDF generation successful.");
                resolve(base64Pdf);
            } catch(concatError) {
                console.error("Error during Buffer.concat or Base64 encoding:", concatError);
                 // Check if error message relates to Buffer incompatibility
                if (concatError.message.includes("not type") || concatError.message.includes("Buffer")) {
                     reject(new Error(`Concatenation Error: Buffer.concat failed, likely incompatible chunk types received from stream. Original error: ${concatError.message}`));
                } else {
                     reject(concatError);
                }
            }
        });

        pdfDoc.on('error', err => {
            console.error("Fatal error during PDF stream generation:", err);
            reject(err);
        });

        pdfDoc.end(); // Finalize the PDF document generation

    } catch (err) {
        // Catch synchronous errors during setup
        console.error("Synchronous error creating PDF document stream:", err);
        reject(err);
    }
});