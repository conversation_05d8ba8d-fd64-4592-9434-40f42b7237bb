<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Letter Size Page with SVG Background</title>
    <link rel="stylesheet" href="https://api.fontshare.com/v2/css?f[]=clash-display@400,500,600&display=swap">
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            background-color: #f0f0f0;
        }

        .p5-letter-container {
            width: 21.59cm;
            height: 27.94cm;
            position: relative;
            background-color: white;
            box-shadow: 0 0 0.35cm rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .p5-background-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .p5-logo-box {
            position: absolute;
            width: 1.62cm;
            height: 1.16cm;
            bottom: 1.06cm;
            right: 1.13cm;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2;
            overflow: hidden;
        }

        .p5-logo-box img {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
            object-fit: contain;
        }

        .p5-page-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.71cm;
        }

        /* New styles for portfolio company update */
        .p5-portfolio-update-header {
            position: absolute;
            top: 3.39cm;
            /* 96px converted to cm */
            left: 2.19cm;
            /* 62px converted to cm */
            color: black;
            font-size: 0.53cm;
            /* 15px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            word-wrap: break-word;
            z-index: 2;
        }

        .p5-company-name-container {
            position: absolute;
            top: 4.33cm;
            /* 120px converted to cm */
            left: 2.19cm;
            /* 62px converted to cm */
            width: 10.58cm;
            /* 300px converted to cm */
            height: 1.06cm;
            /* 30px converted to cm */
            z-index: 2;
            overflow: hidden;
        }

        .p5-website-url {
            position: absolute;
            top: 4.93cm;
            /* 120px converted to cm */
            right: 2.19cm;
            /* 62px converted to cm */
            color: #5CD468;
            font-size: 0.42cm;
            /* 12px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 400;
            line-height: 0.64cm;
            /* 18px converted to cm */
            word-wrap: break-word;
            z-index: 2;
        }

        .p5-investment-metrics-box {
            position: absolute;
            top: 6.25cm;
            /* 177px converted to cm */
            left: 1.87cm;
            /* 53px converted to cm */
            width: 7.79cm;
            /* 279px converted to cm */
            height: 2.54cm;
            /* 72px converted to cm */
            background: rgba(238.83, 238.83, 238.83, 0.83);
            border-radius: 0.39cm;
            /* 11px converted to cm */
            display: grid;
            grid-template-columns: 1fr 1fr;
            z-index: 2;
        }

        .p5-amount-invested {
            justify-self: center;
            align-self: center;
            text-align: center;
        }

        .p5-ownership {
            justify-self: center;
            align-self: center;
            text-align: center;
        }

        .p5-metric-title {
            color: black;
            font-size: 0.42cm;
            /* 12px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 500;
            line-height: 0.64cm;
            /* 18px converted to cm */
            word-wrap: break-word;
        }

        .p5-metric-value {
            color: black;
            font-size: 0.71cm;
            /* 20px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            word-wrap: break-word;
            text-align: left;
            display: flex;
            align-items: center;
            gap: 0.21cm;
            /* 6px gap between text and icon */
        }

        .p5-metric-value svg {
            min-width: 0.56cm;
            /* 16px converted to cm */
            height: 0.56cm;
            /* 16px converted to cm */
        }

        .p5-text-sections-container {
            position: absolute;
            top: 9.49cm;
            /* 269px converted to cm - position of company overview */
            left: 1.87cm;
            /* 53px converted to cm */
            width: 7.79cm;
            /* 279px converted to cm */
            display: flex;
            flex-direction: column;
            gap: 0.75cm;
            /* 28px converted to cm */
            z-index: 2;
        }

        .p5-text-section {
            width: 100%;
        }

        .p5-section-title {
            color: black;
            font-size: 0.42cm;
            /* 12px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            line-height: 0.64cm;
            /* 18px converted to cm */
            word-wrap: break-word;
            margin-bottom: 0.2cm;
            /* 10px converted to cm */
        }

        .p5-section-text {
            color: black;
            font-size: 0.42cm;
            /* 12px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 400;
            line-height: 0.64cm;
            /* 18px converted to cm */
            word-wrap: break-word;
        }

        .p5-key-metrics-box {
            position: absolute;
            top: 6.24cm;
            /* 177px converted to cm */
            left: 10.31cm;
            /* 349px converted to cm */
            width: 8.32cm;
            /* 236px converted to cm */
            background: rgba(209, 246, 209, 0.83);
            border-radius: 0.39cm;
            /* 11px converted to cm */
            padding: 0.71cm;
            /* 20px converted to cm */
            z-index: 2;
        }

        .p5-key-metrics-title {
            color: black;
            font-size: 0.42cm;
            /* 12px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            word-wrap: break-word;
            margin-bottom: 0.71cm;
            /* 20px converted to cm */
        }

        .p5-metrics-list {
            display: flex;
            flex-direction: column;
            height: calc(100% - 1.41cm);
            /* 40px converted to cm */
            justify-content: space-between;
        }

        .p5-metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .p5-metric-label {
            color: black;
            font-size: 0.42cm;
            /* 12px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 500;
            line-height: 1.13cm;
            /* 32px converted to cm */
            word-wrap: break-word;
        }

        .p5-metric-amount {
            color: black;
            font-size: 0.42cm;
            /* 12px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            word-wrap: break-word;
            text-align: right;
        }

        @media print {
            body {
                background-color: transparent;
                margin: 0;
                padding: 0;
            }

            .p5-letter-container {
                margin: 0;
                box-shadow: none;
                width: 21.59cm;
                height: 27.94cm;
            }

            @page {
                margin: 0;
                padding: 0;
                size: letter;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                color-adjust: exact;
            }

            .p5-page-container {
                gap: 0;
            }

            /* Force background images to print */
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .p5-background-svg {
                display: block !important;
                visibility: visible !important;
            }
        }
    </style>
</head>

<body>
    <div class="p5-page-container" id="p5-pageContainer">
        <div class="p5-letter-container">
            <svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg"
                class="p5-background-svg">
                <g clip-path="url(#clip0_2020_2)">
                    <rect width="612" height="792" fill="white" />
                    <g opacity="0.5" filter="url(#filter0_f_2020_2)">
                        <ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint0_linear_2020_2)" />
                    </g>
                </g>
                <defs>
                    <filter id="filter0_f_2020_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse"
                        color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix" />
                        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                        <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2020_2" />
                    </filter>
                    <linearGradient id="paint0_linear_2020_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#D1F15E" />
                        <stop offset="0.94" stop-color="#70E9EF" />
                    </linearGradient>
                    <clipPath id="clip0_2020_2">
                        <rect width="612" height="792" fill="white" />
                    </clipPath>
                </defs>
            </svg>

            <!-- Portfolio Update Content -->
            <div class="p5-portfolio-update-header">{{ $var.portfolio_update_header }}</div>

            <div class="p5-company-name-container">
                <img src="{{ $var.company_logo_url }}" alt="{{ $var.company_name }}"
                    style="max-width: 100%; max-height: 100%; width: auto; height: auto; object-fit: contain; display: block; margin: 0;">
            </div>

            <div class="p5-website-url">{{ $var.website_url }}</div>

            <div class="p5-investment-metrics-box">
                <div class="p5-amount-invested">
                    <div class="p5-metric-value">${{ $var.amount_invested }} <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_45_120)">
                                <path
                                    d="M14.968 0H2.87734C2.30859 0 1.84609 0.4625 1.84609 1.03125C1.84609 1.6 2.30859 2.0625 2.87734 2.0625H12.4742L0.302344 14.2375C-0.100781 14.6406 -0.100781 15.2937 0.302344 15.6969C0.705469 16.1 1.35859 16.1 1.76172 15.6969L13.9336 3.52187V13.1187C13.9336 13.6875 14.3961 14.15 14.9648 14.15C15.5336 14.15 15.9961 13.6875 15.9961 13.1187V1.03125C15.9992 0.4625 15.5367 0 14.968 0Z"
                                    fill="#22CD00" />
                            </g>
                            <defs>
                                <clipPath id="clip0_45_120">
                                    <rect width="16" height="16" fill="white" />
                                </clipPath>
                            </defs>
                        </svg></div>
                    <div class="p5-metric-title">Amt. Invested</div>
                </div>
                <div class="p5-ownership">
                    <div class="p5-metric-value">{{ $var.ownership }}% </div>
                    <div class="p5-metric-title">Ownership</div>
                </div>
            </div>

            <div class="p5-text-sections-container">
                {% for section in $var.text_sections %}
                <div class="p5-text-section">
                    <div class="p5-section-title">{{ section.title }}</div>
                    <div class="p5-section-text">
                        {{ section.content }}
                    </div>
                </div>
                {% endfor %}
            </div>

            <div class="p5-key-metrics-box">
                <div class="p5-key-metrics-title">Key Metrics</div>
                <div class="p5-metrics-list">
                    {% for metric in $var.key_metrics %}
                    <div class="p5-metric-row">
                        <div class="p5-metric-label">{{ metric.label }}</div>
                        <div class="p5-metric-amount">{{ metric.value }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <div class="p5-logo-box">
                <img src="{{ $var.logo_url }}" alt="Logo">
            </div>
        </div>
    </div>
</body>

</html>