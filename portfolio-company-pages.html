<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Company Updates</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900&display=swap">
    <link rel="stylesheet" href="https://api.fontshare.com/v2/css?f[]=clash-display@400,500,600&display=swap">
    <style>
        @font-face {
            font-family: 'Clash Display';
            src: local('Clash Display'), url('https://fonts.cdnfonts.com/s/65008/ClashDisplayRegular.woff') format('woff');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }
        
        @font-face {
            font-family: 'Clash Display';
            src: local('Clash Display'), url('https://fonts.cdnfonts.com/s/65008/ClashDisplaySemibold.woff') format('woff');
            font-weight: 600;
            font-style: normal;
            font-display: swap;
        }

        body {
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            background-color: #f0f0f0;
            padding-top: 20px;
            padding-bottom: 20px;
        }

        @media print {
            body {
                background-color: transparent;
                margin: 0;
                padding: 0;
                gap: 0;
            }

            @page {
                margin: 0;
                padding: 0;
                size: letter;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                color-adjust: exact;
            }
            
            /* Force background images to print */
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>
</head>
<body>
    {% for company in $var.data.portfolio_companies %}
    <!-- Portfolio Company Page -->
    <div style="width: 21.59cm; height: 27.94cm; position: relative; background-color: white; box-shadow: 0 0 0.35cm rgba(0, 0, 0, 0.1); overflow: hidden;">
        <!-- Background SVG -->
        <svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg"
            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover;">
            <g clip-path="url(#clip0_2020_2)">
                <rect width="612" height="792" fill="white" />
                <g opacity="0.5" filter="url(#filter0_f_2020_2)">
                    <ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint0_linear_2020_2)" />
                </g>
            </g>
            <defs>
                <filter id="filter0_f_2020_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse"
                    color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                    <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2020_2" />
                </filter>
                <linearGradient id="paint0_linear_2020_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509"
                    gradientUnits="userSpaceOnUse">
                    <stop stop-color="#D1F15E" />
                    <stop offset="0.94" stop-color="#70E9EF" />
                </linearGradient>
                <clipPath id="clip0_2020_2">
                    <rect width="612" height="792" fill="white" />
                </clipPath>
            </defs>
        </svg>

        <!-- Portfolio Update Content -->
        <div style="position: absolute; top: 3.39cm; left: 2.19cm; color: black; font-size: 0.53cm; font-family: 'Clash Display', sans-serif; font-weight: 600; word-wrap: break-word; z-index: 2;">{{ company.portfolio_update_header }}</div>

        <div style="position: absolute; top: 4.33cm; left: 2.19cm; width: 10.58cm; height: 1.06cm; z-index: 2; overflow: hidden;">
            <img src="{{ company.company_logo_url }}" alt="{{ company.company_name }}"
                style="max-width: 100%; max-height: 100%; width: auto; height: auto; object-fit: contain; display: block; margin: 0;">
        </div>

        <div style="position: absolute; top: 4.93cm; right: 2.19cm; color: #5CD468; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 400; line-height: 0.64cm; word-wrap: break-word; z-index: 2;">{{ company.website_url }}</div>

        <div style="position: absolute; top: 6.25cm; left: 1.87cm; width: 7.79cm; height: 2.54cm; background: rgba(238.83, 238.83, 238.83, 0.83); border-radius: 0.39cm; display: grid; grid-template-columns: 1fr 1fr; z-index: 2;">
            <div style="justify-self: center; align-self: center; text-align: center;">
                <div style="color: black; font-size: 0.71cm; font-family: 'Clash Display', sans-serif; font-weight: 600; word-wrap: break-word; text-align: left; display: flex; align-items: center; gap: 0.21cm;">${{ company.amount_invested }} <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_45_120)">
                            <path
                                d="M14.968 0H2.87734C2.30859 0 1.84609 0.4625 1.84609 1.03125C1.84609 1.6 2.30859 2.0625 2.87734 2.0625H12.4742L0.302344 14.2375C-0.100781 14.6406 -0.100781 15.2937 0.302344 15.6969C0.705469 16.1 1.35859 16.1 1.76172 15.6969L13.9336 3.52187V13.1187C13.9336 13.6875 14.3961 14.15 14.9648 14.15C15.5336 14.15 15.9961 13.6875 15.9961 13.1187V1.03125C15.9992 0.4625 15.5367 0 14.968 0Z"
                                fill="#22CD00" />
                        </g>
                        <defs>
                            <clipPath id="clip0_45_120">
                                <rect width="16" height="16" fill="white" />
                            </clipPath>
                        </defs>
                    </svg></div>
                <div style="color: black; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 500; line-height: 0.64cm; word-wrap: break-word;">Amt. Invested</div>
            </div>
            <div style="justify-self: center; align-self: center; text-align: center;">
                <div style="color: black; font-size: 0.71cm; font-family: 'Clash Display', sans-serif; font-weight: 600; word-wrap: break-word;">{{ company.ownership }}% </div>
                <div style="color: black; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 500; line-height: 0.64cm; word-wrap: break-word;">Ownership</div>
            </div>
        </div>

        <div style="position: absolute; top: 9.49cm; left: 1.87cm; width: 7.79cm; display: flex; flex-direction: column; gap: 0.75cm; z-index: 2;">
            {% for section in company.text_sections %}
            <div style="width: 100%;">
                <div style="color: black; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 600; line-height: 0.64cm; word-wrap: break-word; margin-bottom: 0.2cm;">{{ section.title }}</div>
                <div style="color: black; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 400; line-height: 0.64cm; word-wrap: break-word;">
                    {{ section.content }}
                </div>
            </div>
            {% endfor %}
        </div>

        <div style="position: absolute; top: 6.24cm; left: 10.31cm; width: 8.32cm; background: rgba(209, 246, 209, 0.83); border-radius: 0.39cm; padding: 0.71cm; z-index: 2;">
            <div style="color: black; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 600; word-wrap: break-word; margin-bottom: 0.71cm;">Key Metrics</div>
            <div style="display: flex; flex-direction: column; height: calc(100% - 1.41cm); justify-content: space-between;">
                {% for metric in company.key_metrics %}
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="color: black; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 500; line-height: 1.13cm; word-wrap: break-word;">{{ metric.label }}</div>
                    <div style="color: black; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 600; word-wrap: break-word; text-align: right;">{{ metric.value }}</div>
                </div>
                {% endfor %}
            </div>
        </div>

        <div style="position: absolute; width: 1.62cm; height: 1.16cm; bottom: 1.06cm; right: 1.13cm; display: flex; justify-content: center; align-items: center; z-index: 2; overflow: hidden;">
            <svg width="46" height="33" viewBox="0 0 46 33" fill="none" xmlns="http://www.w3.org/2000/svg" style="max-width: 100%; max-height: 100%; width: auto; height: auto;">
<path d="M0.00137067 20.9346V32.9975H8.10509V19.7046H1.22966C0.550474 19.7046 0 20.2552 0 20.9346H0.00137067Z" fill="#222222"/>
<path d="M17.3624 7.27033C18.0416 7.27033 18.592 6.71971 18.592 6.04034V0H3.49102H1.23161C0.552425 0 0.00195312 0.550616 0.00195312 1.22998V13.097H8.10568V7.27033H17.3624Z" fill="#222222"/>
<path d="M27.5053 20.9346V32.9975H35.609V19.7046H28.7336C28.0544 19.7046 27.5039 20.2552 27.5039 20.9346H27.5053Z" fill="#222222"/>
<path d="M19.0574 13.7188C19.2751 13.334 19.6846 13.097 20.1255 13.097H27.5062V1.22998C27.5062 0.550616 28.0567 0 28.7359 0H30.8994H46.0004V6.04034C46.0004 6.71971 45.45 7.27033 44.7708 7.27033H35.6099V13.097H27.513L19.4025 27.3774H25.0236V31.77C25.0236 32.4494 24.4731 33 23.7939 33H8.10547" fill="#222222"/>
<path d="M35.6113 19.7059H42.2512C43.1399 19.7059 43.8602 18.9855 43.8602 18.0965V13.0972H35.6113V19.7059Z" fill="#222222"/>
<path d="M8.08789 19.7059H11.1675C12.0562 19.7059 12.7765 18.9855 12.7765 18.0965V13.0972H8.08789V19.7059Z" fill="#222222"/>
</svg>
        </div>
    </div>
    {% endfor %}
</body>
</html> 