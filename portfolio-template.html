<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, target-densitydpi=96, user-scalable=no">
    <title>Portfolio Updates Template</title>
    <link rel="stylesheet" href="https://api.fontshare.com/v2/css?f[]=clash-display@400,500,600&display=swap">
    <style>
        /* Base styles for the document */
        /* Define fixed DPI as CSS variables for calculations */
        :root {
            --device-dpi: 96dpi;
            --px-per-cm: 37.7952755906; /* 96 / 2.54 (cm per inch) */
        }
        
        body {
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #f0f0f0;
            padding-top: 20px;
            padding-bottom: 20px;
        }
        
        /* Page container */
        .p6-page {
            width: 21.59cm;
            height: 27.94cm;
            position: relative;
            background-color: white;
            box-shadow: 0 0 0.35cm rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
            page-break-after: always;
        }
        
        /* Common template elements */
        .p6-background-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .p6-header {
            position: absolute;
            top: 3.39cm;
            left: 2.19cm;
            color: black;
            font-size: 0.53cm;
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            z-index: 2;
        }
        
        .p6-logo-box {
            position: absolute;
            width: 1.62cm;
            height: 1.16cm;
            bottom: 1.06cm;
            right: 1.13cm;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2;
        }
        
        /* Container for update items */
        .p6-update-container {
            position: absolute;
            top: 6.05cm;
            left: 2.19cm;
            right: 1.13cm;
            bottom: 3.2cm;
            z-index: 2;
            display: flex;
            flex-direction: column;
            gap: 0.1cm;
            overflow: hidden;
        }
        
        /* Styles for update items */
        .p6-update-item {
            margin-bottom: 1cm;
        }
        
        .p6-company-logo-box {
            width: 4.3cm;
            height: 1.09cm;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            overflow: hidden;
            object-fit: contain;
        }
        
        .p6-company-logo-box img {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
            object-fit: contain;
        }
        
        .p6-fund-boxes-container {
            margin-top: 0.28cm;
            display: flex;
            gap: 0.53cm;
            justify-content: space-between;
            width: 100%;
        }
        
        .p6-fund-box {
            min-width: 1.5cm;
            height: 0.6cm;
            background: #B2D1B2;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Clash Display', sans-serif;
            font-size: 0.32cm;
            padding-left: 0.2cm;
            padding-right: 0.2cm;
            font-weight: 500;
            white-space: nowrap;
        }
        
        .p6-fund-box .p6-rank-value {
            font-weight: 700;
            margin-left: 0.14cm;
        }
        
        .p6-fund-boxes-left {
            display: flex;
            gap: 0.53cm;
        }
        
        .p6-website-text {
            color: #5CD468;
            font-size: 0.42cm;
            font-family: 'Clash Display', sans-serif;
            font-weight: 400;
            line-height: 0.64cm;
            word-wrap: break-word;
            align-self: center;
        }
        
        .p6-report-title {
            color: black;
            font-size: 0.42cm;
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            line-height: 0.64cm;
            word-wrap: break-word;
            margin-top: 0.28cm;
        }
        
        .p6-report-body {
            color: black;
            font-size: 0.42cm;
            font-family: 'Clash Display', sans-serif;
            font-weight: 400;
            line-height: 0.64cm;
            word-wrap: break-word;
            margin-top: 0.28cm;
        }
        
        /* Continuation item styling */
        .p6-continuation-header {
            border-left: 0.1cm solid #5CD468;
            padding-left: 0.4cm;
            margin-bottom: 0.4cm;
        }
        
        .p6-update-item.p6-continuation {
            position: relative;
        }
        
        .p6-update-item.p6-continuation .p6-report-title {
            font-style: italic;
        }
        
        .p6-update-item.p6-continuation .p6-report-body {
            position: relative;
        }
        
        .p6-update-item.p6-continuation::before {
            content: "";
            position: absolute;
            top: 0;
            left: -0.5cm;
            width: 0.1cm;
            height: 100%;
            background-color: #5CD468;
            opacity: 0.3;
        }
        
        /* Specify styles for printing */
        @media print {
            body {
                background-color: transparent;
                margin: 0;
                padding: 0;
            }
            
            .p6-page {
                margin: 0;
                box-shadow: none;
            }
            
            @page {
                margin: 0;
                padding: 0;
                size: letter;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                color-adjust: exact;
            }
            
            /* Force background images to print */
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            .p6-background-svg {
                display: block !important;
                visibility: visible !important;
            }
            
            /* Ensure continuation styling prints properly */
            .p6-update-item.p6-continuation::before,
            .p6-continuation-header {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>
</head>
<body>
    <div id="pages-container"></div>
    
    <!-- Template for individual pages - will be cloned by JavaScript -->
    <template id="page-template">
        <div class="p6-page">
            <!-- Background SVG -->
            <svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg"
                class="p6-background-svg">
                <g clip-path="url(#clip0_2020_2)">
                    <rect width="612" height="792" fill="white" />
                    <g opacity="0.5" filter="url(#filter0_f_2020_2)">
                        <ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint0_linear_2020_2)" />
                    </g>
                </g>
                <defs>
                    <filter id="filter0_f_2020_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse"
                        color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix" />
                        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                        <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2020_2" />
                    </filter>
                    <linearGradient id="paint0_linear_2020_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#D1F15E" />
                        <stop offset="0.94" stop-color="#70E9EF" />
                    </linearGradient>
                    <clipPath id="clip0_2020_2">
                        <rect width="612" height="792" fill="white" />
                    </clipPath>
                </defs>
            </svg>

            <!-- Fixed header element -->
            <div class="p6-header">Portfolio Company Updates</div>

            <!-- Container for update items - will be filled by JavaScript -->
            <div class="p6-update-container"></div>

            <!-- Fixed logo element -->
            <div class="p6-logo-box">
                <img src="rendyr-logo-blackandwhite.svg" alt="Rendyr Logo">
            </div>
        </div>
    </template>
    
    <script src="portfolio-template.js"></script>
</body>
</html> 