<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Letter Size Page with SVG Background</title>
    <link rel="stylesheet" href="https://api.fontshare.com/v2/css?f[]=clash-display@400,500,600&display=swap">
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            background-color: #f0f0f0;
        }

        .p3-letter-container {
            width: 21.59cm;
            height: 27.94cm;
            position: relative;
            background-color: white;
            box-shadow: 0 0 0.35cm rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .p3-background-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .p3-logo-box {
            position: absolute;
            width: 1.62cm;
            /* 46px converted to cm */
            height: 1.16cm;
            /* 33px converted to cm */
            bottom: 1.06cm;
            /* 30px converted to cm */
            right: 1.13cm;
            /* 32px converted to cm */
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2;
            overflow: hidden;
        }

        .p3-logo-box img {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
            object-fit: contain;
        }

        .p3-title {
            position: absolute;
            left: 2.19cm; /* 62px converted to cm */
            top: 3.39cm; /* 96px converted to cm */
            color: black;
            font-size: 0.85cm; /* 24px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            word-wrap: break-word;
            z-index: 2;
        }

        .p3-metrics-container {
            position: absolute;
            left: 2.19cm; /* 62px converted to cm */
            top: 5.40cm; /* 153px converted to cm */
            display: flex;
            gap: 1cm; 
            z-index: 2;
        }

        .p3-metric-box {
            width: 3.53cm;  /* 100px converted to cm */
            height: 2.96cm; /* 84px converted to cm */
            background: rgba(209, 246, 209, 0.83);
            border-radius: 0.39cm; /* 11px converted to cm */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .p3-metric-value {
            color: black;
            font-size: 0.81cm; /* 23px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            word-wrap: break-word;
            text-align: center;
        }

        .p3-metric-description {
            color: black;
            font-size: 0.56cm; /* 16px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 500;
            word-wrap: break-word;
            text-align: center;
            margin-top: 0.25cm;
        }

        .p3-additional-metrics-container {
            position: absolute;
            left: 2.33cm; /* 66px converted to cm */
            top: 9.99cm; /* 283px converted to cm */
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            z-index: 2;
            width: 19cm;
        }

        .p3-additional-metric-box {
            display: flex;
            flex-direction: column;
            padding: 0.5cm;
        }

        .p3-additional-metric-value {
            color: black;
            font-size: 1.09cm; /* 31px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            word-wrap: break-word;
        }

        .p3-additional-metric-description {
            color: #4CD964;
            font-size: 0.56cm; /* 16px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            word-wrap: break-word;
        }

        .p3-cashflow-container {
            position: absolute;
            left: 2.19cm; /* 62px converted to cm */
            top: 16.43cm; /* 466px converted to cm */
            right: 1.94cm;
            /* bottom: 7.27cm; */
            /* 206px converted to cm - calculating from bottom edge */
            background: rgba(238.83, 238.83, 238.83, 0.83);
            border-radius: 0.39cm; /* 11px converted to cm */
            z-index: 2;
            padding: 0;
            
        }

        .p3-cashflow-title {
            color: black;
            font-size: 0.56cm; /* 16px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            word-wrap: break-word;
            margin-top: 0.53cm; /* 15px converted to cm */
            margin-left: 0.7cm;
        }

        .p3-cashflow-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr;
            margin-top: 0.1cm;
            margin-left: 0.4cm;
            margin-bottom: 0.1cm;
            padding: 0.5cm;
            column-gap: 2.5cm;

            /* margin-top: calc( 1.66cm - (0.53cm + 19.4px) ) */
            /* 47px converted to cm */
            /* padding-left: 2cm;  */
            /* Increased from 0.7cm to push further right */
            /* padding-right: 0.7cm; */
        }


        .p3-cashflow-value {
            color: black;
            font-size: 1.09cm; /* 31px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            word-wrap: break-word;
        }

        .p3-cashflow-description {
            color: black;
            font-size: 0.56cm; /* 16px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 400;
            word-wrap: break-word;
            margin-top: 0.25cm;
        }

        .p3-page-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.71cm;
        }

        @media print {
            body {
                background-color: transparent;
                margin: 0;
                padding: 0;
            }

            .p3-letter-container {
                margin: 0;
                box-shadow: none;
                width: 21.59cm;
                /* 612px converted to cm */
                height: 27.94cm;
                /* 792px converted to cm */
            }

            @page {
                margin: 0;
                padding: 0;
                size: letter;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                color-adjust: exact;
            }

            .p3-page-container {
                gap: 0;
            }
            
            /* Force background images to print */
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            .p3-background-svg {
                display: block !important;
                visibility: visible !important;
            }
        }
    </style>
</head>

<body>
    <div class="p3-page-container" id="p3-pageContainer">
        <div class="p3-letter-container">
            <svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg"
                class="p3-background-svg">
                <g clip-path="url(#clip0_2020_2)">
                    <rect width="612" height="792" fill="white" />
                    <g opacity="0.5" filter="url(#filter0_f_2020_2)">
                        <ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint0_linear_2020_2)" />
                    </g>
                </g>
                <defs>
                    <filter id="filter0_f_2020_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse"
                        color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix" />
                        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                        <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2020_2" />
                    </filter>
                    <linearGradient id="paint0_linear_2020_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#D1F15E" />
                        <stop offset="0.94" stop-color="#70E9EF" />
                    </linearGradient>
                    <clipPath id="clip0_2020_2">
                        <rect width="612" height="792" fill="white" />
                    </clipPath>
                </defs>
            </svg>
            <div class="p3-logo-box">
                <img src="https://storage.googleapis.com/x8xh-wdgz-rmil.n7e.xano.io/vault/gQxBdSlG/46fp2_mWEeQzS8H27JjH58q90tI/2SMsUg../rendyr-logo-colored.svg" alt="Logo">
            </div>
            <div class="p3-title">Fund Performance Metrics</div>
            <div class="p3-metrics-container">
                <div class="p3-metric-box">
                    <div class="p3-metric-value">{{ $var.net_irr }}%</div>
                    <div class="p3-metric-description">Net IRR</div>
                </div>
                <div class="p3-metric-box">
                    <div class="p3-metric-value">{{ $var.gross_irr }}%</div>
                    <div class="p3-metric-description">Gross IRR</div>
                </div>
                <div class="p3-metric-box">
                    <div class="p3-metric-value">{{ $var.dpi }}x</div>
                    <div class="p3-metric-description">DPI</div>
                </div>
                <div class="p3-metric-box">
                    <div class="p3-metric-value">{{ $var.rvpi }}x</div>
                    <div class="p3-metric-description">RVPI</div>
                </div>
            </div>
            
            <div class="p3-additional-metrics-container">
                <div class="p3-additional-metric-box">
                    <div class="p3-additional-metric-value">${{ $var.total_capital_deployed }}M</div>
                    <div class="p3-additional-metric-description">Total Capital Deployed to Date</div>
                </div>
                <div class="p3-additional-metric-box">
                    <div class="p3-additional-metric-value">${{ $var.distributions_to_date }}M</div>
                    <div class="p3-additional-metric-description">Distributions to Date</div>
                </div>
                <div class="p3-additional-metric-box">
                    <div class="p3-additional-metric-value">${{ $var.unrealized_value }}M</div>
                    <div class="p3-additional-metric-description">Unrealized Value</div>
                </div>
                <div class="p3-additional-metric-box">
                    <div class="p3-additional-metric-value">{{ $var.portfolio_company_count }}</div>
                    <div class="p3-additional-metric-description">Portfolio Company Count</div>
                </div>
            </div>
            
            <div class="p3-cashflow-container">
                <div class="p3-cashflow-title">Cash Flow Summary</div>
                <div class="p3-cashflow-metrics">
                    <div class="p3-cashflow-metric">
                        <div class="p3-cashflow-value">${{ $var.capital_called_ytd }}M</div>
                        <div class="p3-cashflow-description">Capital Called YTD</div>
                    </div>
                    <div class="p3-cashflow-metric">
                        <div class="p3-cashflow-value">${{ $var.q2_disbursements }}</div>
                        <div class="p3-cashflow-description">Q2 Disbursements</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>