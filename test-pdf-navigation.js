// Test script to generate HTML and verify PDF navigation
const fs = require('fs');
const ejs = require('ejs');

// Load sample data
const sampleData = JSON.parse(fs.readFileSync('sample-data.json', 'utf8'));

console.log('🔍 Testing PDF Navigation Fixes...\n');

// Test 1: Generate simple TOC
console.log('📋 Test 1: Generating Simple TOC...');
try {
    const simpleTocTemplate = fs.readFileSync('toc-simple.ejs', 'utf8');
    const simpleTocHtml = ejs.render(simpleTocTemplate, { toc: sampleData.slice(0, 10) });
    fs.writeFileSync('test-simple-toc.html', simpleTocHtml);
    console.log('✅ Simple TOC generated: test-simple-toc.html');
} catch (error) {
    console.log('❌ Simple TOC failed:', error.message);
}

// Test 2: Generate regular TOC
console.log('\n📋 Test 2: Generating Regular TOC...');
try {
    const tocTemplate = fs.readFileSync('toc.ejs', 'utf8');
    const tocHtml = ejs.render(tocTemplate, { toc: sampleData.slice(0, 10) });
    fs.writeFileSync('test-regular-toc.html', tocHtml);
    console.log('✅ Regular TOC generated: test-regular-toc.html');
} catch (error) {
    console.log('❌ Regular TOC failed:', error.message);
}

// Test 3: Create a minimal test page with anchors
console.log('\n📄 Test 3: Creating Test Page with Anchors...');
const testPageHtml = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>PDF Navigation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .page { height: 800px; border: 1px solid #ccc; margin: 20px 0; padding: 20px; }
        .toc-link { display: block; margin: 10px 0; text-decoration: none; color: blue; }
        .toc-link:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1>Table of Contents</h1>
    ${sampleData.slice(0, 5).map(item => 
        `<a href="#page-${item.page_number}" class="toc-link">${item.company_name} - Page ${item.page_number}</a>`
    ).join('\n    ')}
    
    <hr style="margin: 40px 0;">
    
    ${sampleData.slice(0, 5).map(item => `
    <div class="page" id="page-${item.page_number}">
        <a name="page-${item.page_number}"></a>
        <h2>Page ${item.page_number}: ${item.company_name}</h2>
        <p>This is the content for ${item.company_name}.</p>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
    </div>`).join('\n    ')}
</body>
</html>`;

fs.writeFileSync('test-minimal-navigation.html', testPageHtml);
console.log('✅ Minimal test page generated: test-minimal-navigation.html');

// Test 4: Analyze anchor structure
console.log('\n🔍 Test 4: Analyzing Anchor Structure...');
const testHtml = fs.readFileSync('test.html', 'utf8');

// Count different types of anchors
const nameAnchors = (testHtml.match(/name="page-\d+"/g) || []).length;
const idAnchors = (testHtml.match(/id="page-\d+"/g) || []).length;
const hrefLinks = (testHtml.match(/href="#page-\d+"/g) || []).length;

console.log(`📍 Found ${nameAnchors} anchors with name="page-X"`);
console.log(`🏷️  Found ${idAnchors} elements with id="page-X"`);
console.log(`🔗 Found ${hrefLinks} links with href="#page-X"`);

// Test 5: Check for problematic CSS
console.log('\n🎨 Test 5: Checking for Problematic CSS...');
const problematicPatterns = [
    { pattern: /display:\s*contents/g, issue: 'display: contents breaks PDF links' },
    { pattern: /display:\s*grid.*?<a/g, issue: 'display: grid on links breaks PDF navigation' },
    { pattern: /visibility:\s*hidden.*?<a/g, issue: 'hidden anchors may not work in PDF' }
];

problematicPatterns.forEach(({ pattern, issue }) => {
    const matches = testHtml.match(pattern);
    if (matches) {
        console.log(`⚠️  ${issue}: ${matches.length} instances found`);
    }
});

console.log('\n📋 Summary of Recommendations:');
console.log('1. Use test-simple-toc.html - it has the simplest link structure');
console.log('2. Test test-minimal-navigation.html in your PDF generator');
console.log('3. If links still don\'t work, the issue is with your PDF generation tool');
console.log('4. Consider using PDF bookmarks instead of HTML anchors');

console.log('\n🔧 Next Steps:');
console.log('1. Open test-simple-toc.html in a browser and test links');
console.log('2. Print to PDF and test if links work in the PDF');
console.log('3. If browser links work but PDF links don\'t, it\'s a PDF generator issue');
console.log('4. Try different PDF generation tools (Puppeteer, wkhtmltopdf, etc.)');
