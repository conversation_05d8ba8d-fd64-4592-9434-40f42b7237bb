function generateExitedPositionsPage(data) {
    // Filter out exit companies
    const exitCompanies = data.exit_companies || [];
    
    // Generate all exit company HTML entries
    const exitCompaniesHTML = exitCompanies.map(company => createExitCompanyHTML(company)).join('');
    
    return `
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            background-color: #f0f0f0;
        }

        .p7-letter-container {
            width: 21.59cm;
            height: 27.94cm;
            position: relative;
            background-color: white;
            box-shadow: 0 0 0.35cm rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .p7-background-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .p7-logo-box {
            position: absolute;
            width: 1.62cm;
            height: 1.16cm;
            bottom: 1.06cm;
            right: 1.13cm;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2;
            overflow: hidden;
        }

        .p7-logo-box img {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
            object-fit: contain;
        }

        .p7-company-logo-box {
            width: 4.3cm;
            height: 1.09cm;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            overflow: hidden;
            object-fit: contain;
        }

        .p7-company-logo-box img {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
            object-fit: contain;
        }

        .p7-fund-boxes-container {
            margin-top: 0.28cm;
            display: flex;
            gap: 0.53cm;
            justify-content: space-between;
            width: 100%;
        }

        .p7-fund-box {
            min-width: 1.5cm;
            height: 0.6cm;
            background: #B2D1B2;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Clash Display', sans-serif;
            font-size: 0.32cm;
            padding-left: 0.2cm;
            padding-right: 0.2cm;
            font-weight: 500;
            flex-direction: row;
            white-space: nowrap;
        }

        .p7-fund-box .rank-value {
            font-weight: 700;
            margin-left: 0.14cm;
        }

        .p7-fund-boxes-left {
            display: flex;
            gap: 0.53cm;
        }

        .p7-report-title {
            color: black;
            font-size: 0.42cm;
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            line-height: 0.64cm;
            word-wrap: break-word;
            margin-top: 0.28cm;
        }

        .p7-page-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.7cm;
        }

        .p7-portfolio-update-header {
            position: absolute;
            top: 3.39cm;
            left: 2.19cm;
            color: black;
            font-size: 0.53cm;
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            word-wrap: break-word;
            z-index: 2;
        }

        .p7-company-name-container {
            position: absolute;
            top: 4.33cm;
            left: 2.19cm;
            width: 10.58cm;
            height: 1.06cm;
            z-index: 2;
            overflow: hidden;
        }

        .p7-website-url {
            position: absolute;
            top: 4.93cm;
            right: 2.19cm;
            color: #5CD468;
            font-size: 0.42cm;
            font-family: 'Clash Display', sans-serif;
            font-weight: 400;
            line-height: 0.64cm;
            word-wrap: break-word;
            z-index: 2;
        }

        .update-container {
            position: absolute;
            top: 6.05cm;
            left: 2.19cm;
            right: 1.13cm;
            bottom: 3.2cm;
            z-index: 2;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.7cm;
            grid-column-gap: 1cm;
        }

        .update-item {
            margin-bottom: 0.7cm;
        }

        @media print {
            body {
                background-color: transparent;
                margin: 0;
                padding: 0;
            }

            .p7-letter-container {
                margin: 0;
                box-shadow: none;
                width: 21.59cm;
                height: 27.94cm;
            }

            @page {
                margin: 0;
                padding: 0;
                size: letter;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                color-adjust: exact;
            }

            .p7-page-container {
                gap: 0;
            }

            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .p7-background-svg {
                display: block !important;
                visibility: visible !important;
            }
        }
    </style>
    <div class="p7-page-container" id="p7-pageContainer">
        <div class="p7-letter-container">
            <svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg"
                class="p7-background-svg">
                <g clip-path="url(#clip0_2020_2)">
                    <rect width="612" height="792" fill="white" />
                    <g opacity="0.5" filter="url(#filter0_f_2020_2)">
                        <ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint0_linear_2020_2)" />
                    </g>
                </g>
                <defs>
                    <filter id="filter0_f_2020_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse"
                        color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix" />
                        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                        <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2020_2" />
                    </filter>
                    <linearGradient id="paint0_linear_2020_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#D1F15E" />
                        <stop offset="0.94" stop-color="#70E9EF" />
                    </linearGradient>
                    <clipPath id="clip0_2020_2">
                        <rect width="612" height="792" fill="white" />
                    </clipPath>
                </defs>
            </svg>

            <div class="p7-portfolio-update-header">Florida Founders Exited Positions</div>

            <div class="update-container">
                ${exitCompaniesHTML}
            </div>

            <div class="p7-logo-box">
                <svg width="46" height="33" viewBox="0 0 46 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M0.00137067 20.9346V32.9975H8.10509V19.7046H1.22966C0.550474 19.7046 0 20.2552 0 20.9346H0.00137067Z"
                        fill="#222222" />
                    <path
                        d="M17.3624 7.27033C18.0416 7.27033 18.592 6.71971 18.592 6.04034V0H3.49102H1.23161C0.552425 0 0.00195312 0.550616 0.00195312 1.22998V13.097H8.10568V7.27033H17.3624Z"
                        fill="#222222" />
                    <path
                        d="M27.5053 20.9346V32.9975H35.609V19.7046H28.7336C28.0544 19.7046 27.5039 20.2552 27.5039 20.9346H27.5053Z"
                        fill="#222222" />
                    <path
                        d="M19.0574 13.7188C19.2751 13.334 19.6846 13.097 20.1255 13.097H27.5062V1.22998C27.5062 0.550616 28.0567 0 28.7359 0H30.8994H46.0004V6.04034C46.0004 6.71971 45.45 7.27033 44.7708 7.27033H35.6099V13.097H27.513L19.4025 27.3774H25.0236V31.77C25.0236 32.4494 24.4731 33 23.7939 33H8.10547"
                        fill="#222222" />
                    <path
                        d="M35.6113 19.7059H42.2512C43.1399 19.7059 43.8602 18.9855 43.8602 18.0965V13.0972H35.6113V19.7059Z"
                        fill="#222222" />
                    <path
                        d="M8.08789 19.7059H11.1675C12.0562 19.7059 12.7765 18.9855 12.7765 18.0965V13.0972H8.08789V19.7059Z"
                        fill="#222222" />
                </svg>
            </div>
        </div>
    </div>`;

    // Helper function to create HTML for each exit company
    function createExitCompanyHTML(company) {
        return `
            <div class="update-item">
                <div class="p7-company-logo-box">
                    <img src="${company.logo || `${company.name.toUpperCase().replace(/\s+/g, '')}.png`}" alt="${company.name} Logo">
                </div>
                <div class="p7-fund-boxes-container">
                    <div class="p7-fund-boxes-left">
                        <div class="p7-fund-box">${company.fund || 'Fund'}</div>
                        <div class="p7-fund-box">Rank: <span class="rank-value">Exit</span></div>
                    </div>
                </div>
                <div class="p7-report-container">
                    <div class="p7-report-title">${company.name}</div>
                </div>
            </div>
        `;
    }
}

// This handles the case where we might not have any exit companies
// so we need to create some placeholder content
function createPlaceholderExitContent() {
    return generateExitedPositionsPage({
        exit_companies: [
            {
                name: "Manifest",
                logo: "MANIFEST.png",
                fund: "Fund 1"
            },
            {
                name: "Autonoma",
                logo: "AUTONOMA.png",
                fund: "Angel Led"
            }
        ]
    });
}

// Using $var.data like page6.js, or fallback to placeholder content
return $var.data ? generateExitedPositionsPage($var.data) : createPlaceholderExitContent();
