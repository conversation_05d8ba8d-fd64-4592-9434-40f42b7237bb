/**
 * Portfolio Updates Template System
 * 
 * This script handles the pagination and generation of company update pages,
 * ensuring proper distribution of content across multiple pages with identical structure.
 */

// Debug configuration
const DEBUG = {
    enabled: true,  // Always enable logging
    visualizeSplits: false,  // No visual splits
    logMeasurements: false,  // No measurement logs
    showGrids: false  // No measurement grids
};

document.addEventListener('DOMContentLoaded', function() {
    // No need to check for debug mode in URL anymore
    
    // Load data and initialize the template
    loadPortfolioData()
        .then(data => {
            initializePortfolioTemplate(data);
        })
        .catch(error => {
            console.error('Failed to initialize template:', error);
        });
});

/**
 * Enable debug mode with visual indicators - No longer needed but kept for reference
 */
function enableDebugMode() {
    // Function is now empty - kept for compatibility only
    console.log('Standard logging is enabled by default');
}

/**
 * Loads portfolio data from an external source
 * @returns {Promise<Object>} The portfolio data
 */
async function loadPortfolioData() {
    // In a production environment, this would fetch from an API or file
    // For demo purposes, we'll use a sample data object
    
    try {
        const response = await fetch('sample-data.json');
        if (!response.ok) {
            throw new Error(`HTTP error: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Error loading data:', error);
        return { companies: [] };
    }
}

/**
 * Initializes the portfolio template system with the provided data
 * @param {Object} data - The portfolio company data
 */
function initializePortfolioTemplate(data) {
    const pagesContainer = document.getElementById('pages-container');
    const companies = data.companies || [];
    
    // Clear any existing content
    pagesContainer.innerHTML = '';
    
    // Create the first page
    const firstPage = createNewPage();
    pagesContainer.appendChild(firstPage);
    
    let currentPage = firstPage;
    let currentContainer = currentPage.querySelector('.p6-update-container');
    
    // Calculate available height for content
    const maxContainerHeight = getMaxContainerHeight();
    let remainingHeight = maxContainerHeight;
    
    
    // Sort companies alphabetically by name
    companies.sort((a, b) => a.name.localeCompare(b.name));
    
    // Process each company and distribute across pages
    companies.forEach((company, index) => {
        if (DEBUG.enabled) {
            console.group(`Processing company: ${company.name} (${index + 1}/${companies.length})`);
            console.log(`Remaining height: ${remainingHeight.toFixed(2)}cm`);
        }
        
        // Create full update item
        const fullUpdateItem = createUpdateItemElement(company);
        const fullItemHeight = calculateElementHeight(fullUpdateItem);
        const fullItemHeightPx = calculateElementHeight(fullUpdateItem, true);
        
        if (DEBUG.enabled) {
            console.log(`Full item height: ${fullItemHeight.toFixed(2)}cm (${Math.round(fullItemHeightPx)}px)`);
        }
        
        // Check if entire item fits on current page
        if (fullItemHeight <= remainingHeight) {
            // Item fits entirely - add it to the page
            currentContainer.appendChild(fullUpdateItem);
            remainingHeight -= fullItemHeight;
            
            // Add debug visualization
            if (DEBUG.enabled) {
                console.log(`Item fits entirely. Remaining height: ${remainingHeight.toFixed(2)}cm`);
            }
        } else {
            // Item doesn't fit entirely - try partial header with logo, fund, and title
            if (DEBUG.enabled) {
                console.log(`Item does not fit entirely. Trying partial header...`);
            }
            
            // Try to fit the partial header (logo, fund boxes, and one line of title)
            const partialHeaderResult = createPartialHeaderElement(company);
            const partialHeaderItem = partialHeaderResult.element;
            const remainingBodyText = partialHeaderResult.remainingText;
            const partialHeaderHeight = calculateElementHeight(partialHeaderItem);
            const partialHeaderHeightPx = calculateElementHeight(partialHeaderItem, true);
            
            if (DEBUG.enabled) {
                console.log(`Partial header height: ${partialHeaderHeight.toFixed(2)}cm (${Math.round(partialHeaderHeightPx)}px)`);
            }
            
            // If the partial header fits, add it and continue to next page with body
            if (partialHeaderHeight <= remainingHeight) {
                if (DEBUG.enabled) {
                    console.log(`Partial header fits. Adding to current page.`);
                }
                
                // Add the partial header to the current page
                currentContainer.appendChild(partialHeaderItem);
                remainingHeight -= partialHeaderHeight;
                
                // Create new page for body content
                currentPage = createNewPage();
                pagesContainer.appendChild(currentPage);
                currentContainer = currentPage.querySelector('.p6-update-container');
                remainingHeight = maxContainerHeight;
                
                // Create continuation item with only the remaining text (not duplicating content)
                const companyWithRemainingText = {...company, report_body: remainingBodyText};
                const continuationItem = createContinuationElement(companyWithRemainingText);
                currentContainer.appendChild(continuationItem);
                
                // Update remaining height
                const continuationHeight = calculateElementHeight(continuationItem);
                const continuationHeightPx = calculateElementHeight(continuationItem, true);
                remainingHeight -= continuationHeight;
                
                // Add debug visualization
                if (DEBUG.enabled) {
                    console.log(`Added continuation item. Remaining height: ${remainingHeight.toFixed(2)}cm`);
                }
            } else {
                // Even partial header doesn't fit - start on a new page
                if (DEBUG.enabled) {
                    console.log(`Partial header doesn't fit. Creating new page.`);
                }
                
                // Move to a new page
                currentPage = createNewPage();
                pagesContainer.appendChild(currentPage);
                currentContainer = currentPage.querySelector('.p6-update-container');
                remainingHeight = maxContainerHeight;
                
                // Try again with the full item on the new page
                if (fullItemHeight <= remainingHeight) {
                    // Full item fits on new page
                    currentContainer.appendChild(fullUpdateItem);
                    remainingHeight -= fullItemHeight;
                    
                    if (DEBUG.enabled) {
                        console.log(`Full item fits on new page. Remaining height: ${remainingHeight.toFixed(2)}cm`);
                    }
                } else {
                    // Full item still doesn't fit - need to split body text
                    if (DEBUG.enabled) {
                        console.log(`Full item still doesn't fit on new page. Splitting content...`);
                    }
                    
                    // Add the header to the current page
                    const headerItem = createCompanyHeaderElement(company);
                    currentContainer.appendChild(headerItem);
                    
                    // Update remaining height after adding header
                    const headerHeight = calculateElementHeight(headerItem);
                    remainingHeight -= headerHeight;
                    
                    // Calculate available space for report body text on current page
                    const availableForBody = remainingHeight;
                    
                    if (availableForBody <= 0) {
                        // Not enough space for any body text after header
                        // Create new page for body content
                        currentPage = createNewPage();
                        pagesContainer.appendChild(currentPage);
                        currentContainer = currentPage.querySelector('.p6-update-container');
                        remainingHeight = maxContainerHeight;
                        
                        // Add continuation with full body text
                        const continuationItem = createContinuationElement(company);
                        currentContainer.appendChild(continuationItem);
                        remainingHeight -= calculateElementHeight(continuationItem);
                    } else {
                        // Split the text to fit in available space
                        const { firstPartItem, secondPartItem, firstPartHeight } = splitReportBodyText(
                            company, 
                            availableForBody
                        );
                        
                        // Replace the header with the first part
                        currentContainer.removeChild(headerItem);
                        currentContainer.appendChild(firstPartItem);
                        remainingHeight = maxContainerHeight - firstPartHeight;
                        
                        // Create new page for second part
                        currentPage = createNewPage();
                        pagesContainer.appendChild(currentPage);
                        currentContainer = currentPage.querySelector('.p6-update-container');
                        remainingHeight = maxContainerHeight;
                        
                        // Add second part to new page
                        currentContainer.appendChild(secondPartItem);
                        remainingHeight -= calculateElementHeight(secondPartItem);
                    }
                }
            }
        }
        
        if (DEBUG.enabled) {
            console.groupEnd();
        }
    });
    
    // Log pagination info
    const totalPages = pagesContainer.querySelectorAll('.p6-page').length;
    console.log(`Created ${totalPages} pages for ${companies.length} companies`);
    
}

/**
 * Creates a new page from the template
 * @returns {HTMLElement} The new page element
 */
function createNewPage() {
    const pageTemplate = document.getElementById('page-template');
    const newPage = document.importNode(pageTemplate.content, true).firstElementChild;
    return newPage;
}

/**
 * Calculates the maximum height available for update items
 * @returns {number} The maximum height in cm
 */
function getMaxContainerHeight() {
    // Fixed container dimensions from CSS
    // top: 6.05cm, bottom: 3.2cm in a letter page (27.94cm)
    // Add a small safety margin (0.2cm) to prevent overflow
    return 27.94 - 6.05 - 3.2 - 0.2;
}

/**
 * Calculates the height an element would have when rendered
 * @param {HTMLElement} element - The element to measure
 * @param {boolean} returnRawPx - If true, returns the raw pixel value instead of converted cm
 * @returns {number} The element's height in cm (or px if returnRawPx is true)
 */
function calculateElementHeight(element, returnRawPx = false) {
    // Create a temporary container for measurement with the correct width constraints
    const tempContainer = document.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.visibility = 'hidden';
    
    // Use exact container dimensions from CSS - left: 2.19cm, right: 1.13cm from a 21.59cm page width
    const pageWidth = 21.59; // Letter width in cm
    const leftMargin = 2.19; // Left margin in cm
    const rightMargin = 1.13; // Right margin in cm
    const contentWidth = pageWidth - leftMargin - rightMargin; // Actual content width
    tempContainer.style.width = contentWidth + 'cm';
    
    tempContainer.style.left = '-9999px';
    tempContainer.style.top = '-9999px';
    
    // Clone the element to avoid modifying the original
    const clone = element.cloneNode(true);
    tempContainer.appendChild(clone);
    document.body.appendChild(tempContainer);
    
    // Force layout recalculation to ensure accurate measurements
    void tempContainer.offsetHeight;
    
    // Use getBoundingClientRect for more accurate measurement
    const rect = clone.getBoundingClientRect();
    const heightPx = rect.height;
    
    // Get computed margins for more accuracy
    const computedStyle = window.getComputedStyle(clone);
    const marginBottom = parseFloat(computedStyle.marginBottom) || 0;
    
    // Include margins in total height with a small buffer based on content length
    // Add extra buffer for elements with more text content
    let buffer = 0;
    const textContent = clone.textContent || '';
    if (textContent.length > 300) {
        // Add more buffer for longer text content
        buffer = marginBottom * 0.15; // Add 15% extra for long content
    }
    
    const totalHeightPx = heightPx + marginBottom + buffer;
    
    // Return raw pixels if requested (useful for debugging)
    if (returnRawPx) {
        document.body.removeChild(tempContainer);
        return totalHeightPx;
    }
    
    // Get the px per cm value from CSS variable
    const style = getComputedStyle(document.documentElement);
    const pxPerCm = parseFloat(style.getPropertyValue('--px-per-cm')) || 37.7952755906; // Default: 96/2.54
    
    const totalHeightCm = totalHeightPx / pxPerCm;
    
    // Remove the temporary container
    document.body.removeChild(tempContainer);
    
    if (DEBUG.enabled && DEBUG.logMeasurements) {
        console.log(`Element height: ${totalHeightCm.toFixed(2)}cm (${Math.round(totalHeightPx)}px) using ${pxPerCm.toFixed(2)} px/cm`);
    }
    
    return totalHeightCm;
}

/**
 * Creates a complete update item element from company data
 * @param {Object} company - The company data
 * @returns {HTMLElement} The update item element
 */
function createUpdateItemElement(company) {
    const updateItem = document.createElement('div');
    updateItem.className = 'p6-update-item';
    updateItem.setAttribute('data-company-name', company.name);
    
    // HTML structure for the update item
    updateItem.innerHTML = `
        <div class="p6-company-logo-box">
            <img src="${company.logo}" alt="${company.name} Logo">
        </div>
        <div class="p6-fund-boxes-container">
            <div class="p6-fund-boxes-left">
                <div class="p6-fund-box">${company.fund}</div>
                <div class="p6-fund-box">Rank: <span class="p6-rank-value">${company.rank}</span></div>
            </div>
            <div class="p6-website-text">${company.website}</div>
        </div>
        <div class="p6-report-container">
            <div class="p6-report-title">
                ${company.name}
            </div>
            <div class="p6-report-body">
                ${company.report_body}
            </div>
        </div>
    `;
    
    return updateItem;
}

/**
 * Creates just the header part of a company update (logo, fund, title)
 * @param {Object} company - The company data
 * @returns {HTMLElement} The header element
 */
function createCompanyHeaderElement(company) {
    const headerItem = document.createElement('div');
    headerItem.className = 'p6-update-item';
    headerItem.setAttribute('data-company-name', company.name);
    
    // HTML structure for just the header parts
    headerItem.innerHTML = `
        <div class="p6-company-logo-box">
            <img src="${company.logo}" alt="${company.name} Logo">
        </div>
        <div class="p6-fund-boxes-container">
            <div class="p6-fund-boxes-left">
                <div class="p6-fund-box">${company.fund}</div>
                <div class="p6-fund-box">Rank: <span class="p6-rank-value">${company.rank}</span></div>
            </div>
            <div class="p6-website-text">${company.website}</div>
        </div>
        <div class="p6-report-container">
            <div class="p6-report-title">
                ${company.name}
            </div>
            <div class="p6-report-body">
            </div>
        </div>
    `;
    
    return headerItem;
}

/**
 * Creates a continuation element for a company update (when split across pages)
 * @param {Object} company - The company data
 * @returns {HTMLElement} The continuation element
 */
function createContinuationElement(company) {
    const continuationItem = document.createElement('div');
    continuationItem.className = 'p6-update-item p6-continuation';
    continuationItem.setAttribute('data-company-name', company.name);
    
    // HTML structure for continuation with a subtle header
    continuationItem.innerHTML = `
        <div class="p6-continuation-header">
            <div class="p6-report-title" style="margin-bottom: 0.2cm; color: #888;">
                ${company.name} (continued)
            </div>
        </div>
        <div class="p6-report-container">
            <div class="p6-report-body">
                ${company.report_body}
            </div>
        </div>
    `;
    
    return continuationItem;
}

/**
 * Creates a partial header element with just logo, fund info, and title line
 * @param {Object} company - The company data
 * @returns {Object} Object containing the partial header element and remaining text
 */
function createPartialHeaderElement(company) {
    const headerItem = document.createElement('div');
    headerItem.className = 'p6-update-item p6-partial-header';
    headerItem.setAttribute('data-company-name', company.name);
    
    // Get just the first sentence of report_body for the partial header
    let firstLine = '';
    let remainingText = '';
    const fullText = company.report_body;
    const endMarkers = ['. ', '? ', '! '];
    
    // Find the first sentence end or use the first 120 characters if no sentence end found
    let firstEndIndex = -1;
    let cutoffIndex = 0;
    
    // Find the first sentence end
    for (const marker of endMarkers) {
        const index = fullText.indexOf(marker);
        if (index !== -1 && (firstEndIndex === -1 || index < firstEndIndex)) {
            firstEndIndex = index + marker.length;
        }
    }
    
    // If no sentence end found or sentence too long, cap at reasonable length
    if (firstEndIndex === -1) {
        // No sentences found, just take a fixed portion
        cutoffIndex = Math.min(120, fullText.length);
        firstLine = fullText.substring(0, cutoffIndex);
        if (fullText.length > 120) {
            firstLine += '...';
        }
    } else if (firstEndIndex > 120) {
        // First sentence is too long, cap it
        cutoffIndex = 120;
        firstLine = fullText.substring(0, cutoffIndex) + '...';
    } else {
        // We found the first sentence and it's a reasonable length
        cutoffIndex = firstEndIndex;
        firstLine = fullText.substring(0, cutoffIndex);
    }
    
    // Get the remaining text after the first part
    remainingText = fullText.substring(cutoffIndex);
    
    // HTML structure for header parts, title line, and first sentence of body
    headerItem.innerHTML = `
        <div class="p6-company-logo-box">
            <img src="${company.logo}" alt="${company.name} Logo">
        </div>
        <div class="p6-fund-boxes-container">
            <div class="p6-fund-boxes-left">
                <div class="p6-fund-box">${company.fund}</div>
                <div class="p6-fund-box">Rank: <span class="p6-rank-value">${company.rank}</span></div>
            </div>
            <div class="p6-website-text">${company.website}</div>
        </div>
        <div class="p6-report-container">
            <div class="p6-report-title">
                ${company.name}
            </div>
            <div class="p6-report-body">
                ${firstLine}
            </div>
        </div>
    `;
    
    return {
        element: headerItem,
        remainingText: remainingText
    };
}

/**
 * Splits a company report body text to fit in available space
 * @param {Object} company - The company data
 * @param {number} availableHeight - Available height in cm
 * @returns {Object} First part item, second part item, and first part height
 */
function splitReportBodyText(company, availableHeight) {
    // Start with the full text and split by characters for more precise control
    const fullText = company.report_body;
    
    // For partial headers: identify the first line to exclude from continuation
    let firstLineEndIndex = -1;
    const endMarkers = ['. ', '? ', '! '];
    
    // Find the first sentence end
    for (const marker of endMarkers) {
        const index = fullText.indexOf(marker);
        if (index !== -1 && (firstLineEndIndex === -1 || index < firstLineEndIndex)) {
            firstLineEndIndex = index + marker.length;
        }
    }
    
    // If no sentence end found or sentence too long, cap at reasonable length
    if (firstLineEndIndex === -1 || firstLineEndIndex > 80) {
        firstLineEndIndex = Math.min(80, fullText.length);
    }
    
    // Binary search approach to find optimal split point based on characters
    let minChars = 1;
    let maxChars = fullText.length;
    let midChars = Math.floor((minChars + maxChars) / 2);
    let bestSplit = midChars;
    let firstPartHeight = 0;
    
    // Create elements for measurement
    const firstPartItem = createCompanyHeaderElement(company);
    const firstPartBody = firstPartItem.querySelector('.p6-report-body');
    
    // For debugging - track iterations and height calculations
    let debugInfo = [];
    
    // Binary search for optimal text split point
    for (let i = 0; i < 10; i++) { // Limit iterations to prevent infinite loops
        // Try with current character count
        // Find the nearest word boundary to avoid splitting mid-word
        let splitIndex = midChars;
        
        // Look for the nearest space character
        while (splitIndex > 0 && fullText[splitIndex] !== ' ') {
            splitIndex--;
        }
        
        // If we went all the way back to the beginning, just use the original midpoint
        if (splitIndex === 0) {
            splitIndex = midChars;
        }
        
        const testText = fullText.substring(0, splitIndex);
        firstPartBody.textContent = testText;
        
        // Measure the height of this text with direct DOM measurement
        const totalHeight = calculateElementHeight(firstPartItem);
        
        // Store debug info if in debug mode
        if (DEBUG.enabled) {
            debugInfo.push({
                iteration: i, 
                charactersUsed: splitIndex,
                height: totalHeight,
                heightRawPx: calculateElementHeight(firstPartItem, true),
                fits: totalHeight <= availableHeight
            });
        }
        
        if (totalHeight <= availableHeight) {
            // This fits, try using more characters
            minChars = midChars;
            bestSplit = splitIndex;
            firstPartHeight = totalHeight;
        } else {
            // Too big, try using fewer characters
            maxChars = midChars;
        }
        
        // Update midpoint for next iteration
        midChars = Math.floor((minChars + maxChars) / 2);
        
        // If we're not making progress, stop
        if (Math.abs(bestSplit - midChars) < 10) break;
    }
    
    // Log debug info if in debug mode
    if (DEBUG.enabled) {
        console.log('Text split optimization for ' + company.name + ':');
        console.table(debugInfo);
    }
    
    // Find a clean break point near our best split (at a sentence ending if possible)
    // Look for period, question mark, or exclamation mark followed by space
    let cleanBreakPoint = bestSplit;
    
    // Search backward from bestSplit for a good break point, but don't go too far back
    const searchRange = Math.min(100, bestSplit); // Look back at most 100 characters
    const textToSearch = fullText.substring(bestSplit - searchRange, bestSplit);
    
    for (const marker of endMarkers) {
        const lastIndex = textToSearch.lastIndexOf(marker);
        if (lastIndex !== -1) {
            cleanBreakPoint = bestSplit - searchRange + lastIndex + marker.length;
            break;
        }
    }
    
    // Create the final first part with optimal text amount
    const firstPartText = fullText.substring(0, cleanBreakPoint) + '...';
    firstPartBody.textContent = firstPartText;
    
    // Create the second part with remaining text - skipping the first line
    const secondPartItem = createContinuationElement(company);
    const secondPartBody = secondPartItem.querySelector('.p6-report-body');
    const continuationText = fullText.substring(Math.max(cleanBreakPoint, firstLineEndIndex));
    secondPartBody.textContent = continuationText;
    
    return {
        firstPartItem: firstPartItem,
        secondPartItem: secondPartItem,
        firstPartHeight: firstPartHeight
    };
}

