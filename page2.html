<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Letter Size Page with SVG Background</title>
    <link rel="stylesheet" href="https://api.fontshare.com/v2/css?f[]=clash-display@400,600&display=swap">
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            background-color: #f0f0f0;
        }

        .p2-letter-container {
            width: 21.59cm;
            height: 27.94cm;
            margin: 0.71cm auto;
            /* 20px converted to cm */
            position: relative;
            background-color: white;
            box-shadow: 0 0 0.35cm rgba(0, 0, 0, 0.1);
            /* 10px converted to cm */
            overflow: hidden;
        }

        .p2-background-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .p2-logo-box {
            position: absolute;
            width: 1.62cm;
            /* 46px converted to cm */
            height: 1.16cm;
            /* 33px converted to cm */
            bottom: 1.06cm;
            /* 30px converted to cm */
            right: 1.13cm;
            /* 32px converted to cm */
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2;
            overflow: hidden;
            /* Prevent overflow of oversized images */
        }

        .p2-logo-box img {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
            object-fit: contain;
            /* Maintain aspect ratio */
        }

        .p2-logo-placeholder {
            font-size: 0.8cm;
            color: #999;
            text-align: center;
        }

        .p2-text-box {
            position: absolute;
            top: 3.39cm;
            /* 96px converted to cm */
            left: 2.19cm;
            /* 62px converted to cm */
            bottom: 3.39cm;
            /* 96px converted to cm */
            width: 16.90cm;
            /* 479px converted to cm */
            /* border: 1px dashed #ccc; */
            /* To be removed */
            overflow: hidden;
            /* Changed from auto to hidden */
            font-family: 'Clash Display', sans-serif;
            z-index: 1;
        }

        .p2-title-box {
            width: 100%;
            height: 1.45cm;
            /* 41px converted to cm */
            display: flex;
            align-items: center;
        }

        .p2-title-text {
            color: black;
            font-size: 0.85cm;
            /* 24px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            word-wrap: break-word;
            width: 100%;
        }

        .p2-content-container {
            margin-top: 0.81cm;
            /* 23px converted to cm */
        }

        .p2-content-text {
            color: black;
            font-size: 0.42cm;
            /* 12px converted to cm */
            font-family: 'Clash Display', sans-serif;
            font-weight: 400;
            line-height: 0.63cm;
            /* 18px converted to cm */
            word-wrap: break-word;
        }

        .p2-page-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.71cm;
            /* 20px converted to cm */
        }

        @media print {
            body {
                background-color: transparent;
                margin: 0;
                padding: 0;
            }

            .p2-letter-container {
                margin: 0;
                box-shadow: none;
                width: 21.59cm;
                /* 612px converted to cm */
                height: 27.94cm;
                /* 792px converted to cm */
            }

            /* Add @page rule to remove browser print margins */
            @page {
                margin: 0;
                padding: 0;
                size: letter;
            }

            /* Remove page-container gap */
            .p2-page-container {
                gap: 0;
            }
        }
    </style>
</head>

<body>
    <div class="p2-page-container" id="p2-pageContainer">
        <div class="p2-letter-container">
            <svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg"
                class="p2-background-svg">
                <g clip-path="url(#clip0_2020_2)">
                    <rect width="612" height="792" fill="white" />
                    <g opacity="0.5" filter="url(#filter0_f_2020_2)">
                        <ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint0_linear_2020_2)" />
                    </g>
                </g>
                <defs>
                    <filter id="filter0_f_2020_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse"
                        color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix" />
                        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                        <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2020_2" />
                    </filter>
                    <linearGradient id="paint0_linear_2020_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#D1F15E" />
                        <stop offset="0.94" stop-color="#70E9EF" />
                    </linearGradient>
                    <clipPath id="clip0_2020_2">
                        <rect width="612" height="792" fill="white" />
                    </clipPath>
                </defs>
            </svg>
            <div class="p2-logo-box">
                <img src="{{ $var.logo_url }}" alt="Logo">
            </div>

            <div class="p2-text-box" id="p2-textBoxId">
                <div class="p2-title-box">
                    <div class="p2-title-text">{{ $var.executive_summary_title }}</div>
                </div>
                {% for paragraph in $var.paragraphs %}
                <div class="p2-content-container">
                    <p class="p2-content-text">{{ paragraph }}</p>
                </div>
                {% endfor %}
            </div>

        </div>
    </div>
</body>

</html>