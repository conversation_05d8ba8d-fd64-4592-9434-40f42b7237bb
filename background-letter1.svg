<svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_2008_2)">
    <rect width="612" height="792" fill="white"/>
    <g opacity="0.4" filter="url(#filter0_f_2008_2)">
    <path d="M-45.1781 411.113C-49.6944 415.201 -56.0779 416.523 -61.8466 414.611L-158.41 382.604C-158.499 382.574 -158.41 382.604 -158.499 382.574L9.49567 231.05L-64.0465 206.674L-45.0115 149.247C-42.0676 140.365 -32.4795 135.554 -23.5936 138.499L181.661 206.534" fill="url(#paint0_linear_2008_2)"/>
    </g>
    <rect opacity="0.4" x="614.354" y="595.621" width="72.0003" height="362.138" transform="rotate(-150 614.354 595.621)" fill="url(#paint1_linear_2008_2)"/>
    <g opacity="0.5" filter="url(#filter1_f_2008_2)">
    <ellipse cx="623" cy="486.499" rx="94" ry="93.5" fill="url(#paint2_linear_2008_2)"/>
    </g>
    <g opacity="0.5" filter="url(#filter2_f_2008_2)">
    <ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint3_linear_2008_2)"/>
    </g>
    </g>
    <defs>
    <filter id="filter0_f_2008_2" x="-222.499" y="73.6331" width="468.161" height="405.838" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
    <feGaussianBlur stdDeviation="32" result="effect1_foregroundBlur_2008_2"/>
    </filter>
    <filter id="filter1_f_2008_2" x="329" y="192.999" width="588" height="587" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
    <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2008_2"/>
    </filter>
    <filter id="filter2_f_2008_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
    <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2008_2"/>
    </filter>
    <linearGradient id="paint0_linear_2008_2" x1="273.858" y1="154.945" x2="-484.695" y2="392.721" gradientUnits="userSpaceOnUse">
    <stop stop-color="#D1F15E"/>
    <stop offset="0.94" stop-color="#70E9EF"/>
    </linearGradient>
    <linearGradient id="paint1_linear_2008_2" x1="587.2" y1="991.104" x2="845.275" y2="953.261" gradientUnits="userSpaceOnUse">
    <stop stop-color="#D1F15E"/>
    <stop offset="0.94" stop-color="#70E9EF"/>
    </linearGradient>
    <linearGradient id="paint2_linear_2008_2" x1="62.732" y1="735.245" x2="747.74" y2="-77.0686" gradientUnits="userSpaceOnUse">
    <stop stop-color="#D1F15E"/>
    <stop offset="0.94" stop-color="#70E9EF"/>
    </linearGradient>
    <linearGradient id="paint3_linear_2008_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509" gradientUnits="userSpaceOnUse">
    <stop stop-color="#D1F15E"/>
    <stop offset="0.94" stop-color="#70E9EF"/>
    </linearGradient>
    <clipPath id="clip0_2008_2">
    <rect width="612" height="792" fill="white"/>
    </clipPath>
    </defs>
    </svg>
    